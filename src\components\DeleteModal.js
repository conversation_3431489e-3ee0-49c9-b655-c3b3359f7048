import Button from "./But<PERSON>";
import Modal from "./Modal";

export default function DeleteModal({ visible, onCancel, onDelete }) {
    return (<Modal visible={visible} className="bg-white p-0">
        <div className="bg-orange/10 p-8">
            <p className="font-bold text-darkblue text-xl pb-6 text-center">Are you sure?</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <Button onClick={onDelete} className="from-error to-red-300">Yes</Button>
                <Button onClick={onCancel}>Cancel</Button>
            </div>
        </div>
    </Modal>)
}