'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import getImageUrl from '@/services/imageURL';
import { useWishes } from '@/context/WishProvider';
import { useAuth } from '@/context/UserProvider';
import GratitudeModal from '@/components/GratitudeModal';
import BackButton from '@/components/BackButton';
import sleep from '@/utils/sleep';
import Divider from '@/components/Divider';
import { useFriends } from '@/context/FriendsProvider';

export default function WishDetailsPage() {
    const router = useRouter();
    const { id: wishId } = useParams();
    const { fetchWishDetails, wishDetails, wishBlessings, pinUnpinWishById } = useWishes();
    const { accessToken, user } = useAuth();
    const { friends, sendRequest } = useFriends();

    const [gratitudeModalOpen, setGratitudeModalOpen] = useState(false);
    const [selectedBlessingId, setSelectedBlessingId] = useState(null);
    const [gratitudeType, setGratitudeType] = useState("PERSONALISED");
    const [leaveAnimation, setLeaveAnimation] = useState(false);

    const isFriend = (userId) => {
        return friends.some(friend => friend._id === userId);
    };

    const handleSendFriendRequest = async (userId) => {
        await sendRequest(userId);
    };

    useEffect(() => {
        if (accessToken && wishId) {
            fetchWishDetails(wishId);
        }
    }, [accessToken, wishId, fetchWishDetails]);

    const handleGoBack = async () => {
        setLeaveAnimation(true)
        await sleep(520)
        router.push('/wishes');
    }

    if (!wishDetails) {
        return <div className="p-4">Loading wish...</div>;
    }


    return (
        <div className={`md:py-17 h-full overflow-y-auto overflow-x-hidden relative ${leaveAnimation ? 'animate-slide-right-out' : ''}`}>
            <div className="p-6 max-w-3xl pb-20 md:pb-auto min-h-full md:min-h-auto md:mt-10 mx-auto backdrop-blur-md shadow-md lightbeige md:rounded-2xl">
                <h1 className="text-2xl mt-4 mb-8 font-heading text-center text-darkgreen">{wishDetails.title}</h1>
                <Divider />
                <BackButton
                    className='absolute top-12 left-6'
                    onClick={handleGoBack}
                />
                <p className="text-gray-600 mb-4">{wishDetails.description}</p>

                {wishDetails.image && (
                    <img
                        src={getImageUrl(wishDetails.image)}
                        alt="Wish"
                        className="w-60 md:w-60 mx-auto rounded-xl mb-4 object-cover"
                    />
                )}


                <button
                    className="px-4 py-2 bg-blue-600 text-white rounded-full mb-4"
                    onClick={() => router.push(`/wishes/${wishId}/blessings`)}
                >
                    Bless this Wish
                </button>
                <pre>
                    <code className="text-sm text-white-700 whitespace-pre-wrap">
                        {JSON.stringify(wishDetails, null, 2)}
                    </code>
                </pre>

                <h2 className="text-xl font-semibold mt-4">Blessings:</h2>
                <ul className="space-y-4 mb-4">
                    {(wishBlessings || []).map((blessing) => (
                        <li key={blessing._id} className="mb-2 bg-white rounded-lg shadow-sm overflow-hidden">
                            <div className="flex items-start justify-between p-4 border-b">
                                <div className="flex items-center">
                                    <div className="w-10 h-10 rounded-full bg-gray-300 mr-3 overflow-hidden">
                                        {blessing.userDetail?.profilePicture ? (
                                            <img
                                                src={blessing.userDetail.profilePicture}
                                                alt={blessing.userDetail.name}
                                                className="w-10 h-10 rounded-full object-cover"
                                            />
                                        ) : (
                                            <div className="w-10 h-10 flex items-center justify-center bg-blue-100 text-blue-500 font-bold">
                                                {blessing.userDetail?.name?.charAt(0) || '?'}
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <p className="font-medium">{blessing.userDetail?.name}</p>
                                        <p className="text-xs text-gray-500">{new Date(blessing.created).toLocaleDateString()}</p>
                                    </div>
                                </div>

                                <div className="flex items-center space-x-2">
                                    {blessing.userDetail && blessing.userDetail._id !== user?._id && (
                                        isFriend(blessing.userDetail._id) ? (
                                            <span className="text-green-500 text-sm">Friend</span>
                                        ) : (
                                            <button
                                                onClick={() => handleSendFriendRequest(blessing.userDetail._id)}
                                                className="text-sm px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
                                            >
                                                Add Friend
                                            </button>
                                        )
                                    )}

                                    {blessing.userDetail && blessing.userDetail._id !== user?._id && (
                                        <button
                                            onClick={() => {
                                                setSelectedBlessingId(blessing._id);
                                                setGratitudeType("PERSONALISED");
                                                setGratitudeModalOpen(true);
                                            }}
                                            className="text-sm px-2 py-1 bg-green-500 text-white rounded hover:bg-green-600"
                                        >
                                            Give Gratitude
                                        </button>
                                    )}
                                </div>
                            </div>

                            <div className="p-4">
                                {blessing.notes && (
                                    <p className="text-gray-700 mb-4">{blessing.notes}</p>
                                )}

                                {blessing.audio && (
                                    <div className="mb-4">
                                        <audio controls className="w-full">
                                            <source src={blessing.audio} type="audio/mpeg" />
                                            Your browser does not support the audio element.
                                        </audio>
                                    </div>
                                )}

                                {/* Display gratitudes for this blessing */}
                                {blessing.gratitudeDetails && blessing.gratitudeDetails.length > 0 && (
                                    <div className="mt-4">
                                        <h3 className="text-sm font-semibold text-gray-700 mb-2">Gratitudes:</h3>
                                        <div className="space-y-3">
                                            {blessing.gratitudeDetails.map((gratitude) => (
                                                <div key={gratitude._id} className="bg-gray-50 p-3 rounded-lg">
                                                    <div className="flex items-center mb-2">
                                                        <div className="w-8 h-8 rounded-full bg-gray-300 mr-2 overflow-hidden">
                                                            {gratitude.userDetail?.profilePicture ? (
                                                                <img
                                                                    src={gratitude.userDetail.profilePicture}
                                                                    alt={gratitude.userDetail.name}
                                                                    className="w-8 h-8 rounded-full object-cover"
                                                                />
                                                            ) : (
                                                                <div className="w-8 h-8 flex items-center justify-center bg-blue-100 text-blue-500 font-bold">
                                                                    {gratitude.userDetail?.name?.charAt(0) || '?'}
                                                                </div>
                                                            )}
                                                        </div>
                                                        <div>
                                                            <p className="text-sm font-medium">{gratitude.userDetail?.name || 'Anonymous'}</p>
                                                            <p className="text-xs text-gray-500">{new Date(gratitude.created).toLocaleDateString()}</p>
                                                        </div>
                                                    </div>

                                                    {gratitude.notes && (
                                                        <p className="text-sm text-gray-700 mb-2">{gratitude.notes}</p>
                                                    )}

                                                    {gratitude.audio && (
                                                        <div className="mb-2">
                                                            <audio controls className="w-full h-8">
                                                                <source src={gratitude.audio} type="audio/mpeg" />
                                                                Your browser does not support the audio element.
                                                            </audio>
                                                        </div>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </li>
                    ))}
                </ul>
                <div className="text-sm text-gray-500">
                    Created by: {wishDetails.userDetail?.firstName || 'Unknown'} {wishDetails.userDetail?.lastName || ''}
                </div>
                <GratitudeModal
                    isOpen={gratitudeModalOpen}
                    onClose={() => setGratitudeModalOpen(false)}
                    wishId={wishId}
                    blessingId={selectedBlessingId}
                    gratitudeType={gratitudeType}
                />
            </div>
        </div>
    );
}
