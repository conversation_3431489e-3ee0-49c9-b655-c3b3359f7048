"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/context/UserProvider";

export default function EditProfilePage() {
    const router = useRouter();
    const { userProfile, user, updateUserProfile, updateUserPicture, updateSettings } = useAuth();

    const [formData, setFormData] = useState({
        firstName: userProfile.data?.firstName || user?.firstName || "",
        lastName: userProfile.data?.lastName || user?.lastName || "",
        phoneNumber: userProfile.data?.phoneNumber || user?.phoneNumber || "",
        profilePicture: userProfile.data?.profilePicture || user?.profilePicture || "",
        countryCode: userProfile.data?.countryCode || user?.countryCode || "",
        countryFlagCode: userProfile.data?.countryFlagCode || user?.countryFlagCode || "",
        pushNotificationStatus: userProfile.data?.pushNotificationStatus ?? true,
        communityNotificationStatus: userProfile.data?.communityNotificationStatus ?? true,
        wishesNotificationStatus: userProfile.data?.wishesNotificationStatus ?? true,
        gratitudeNotificationStatus: userProfile.data?.gratitudeNotificationStatus ?? true,
        perferMedidation: userProfile.data?.perferMedidation ?? false,
        preferPrayer: userProfile.data?.preferPrayer ?? false,
        locationSharing: userProfile.data?.locationSharing ?? false,
    });

    const [imageFile, setImageFile] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === "checkbox" ? checked : value
        }));
    };

    const handleImageChange = (e) => {
        if (e.target.files.length > 0) {
            setImageFile(e.target.files[0]);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError(null);

        try {
            const profileFields = {
                firstName: formData.firstName,
                lastName: formData.lastName,
                phoneNumber: formData.phoneNumber,
                countryCode: formData.countryCode,
                countryFlagCode: formData.countryFlagCode,
            };

            await updateUserProfile(profileFields);
            await updateSettings({
                pushNotificationStatus: formData.pushNotificationStatus,
                communityNotificationStatus: formData.communityNotificationStatus,
                wishesNotificationStatus: formData.wishesNotificationStatus,
                gratitudeNotificationStatus: formData.gratitudeNotificationStatus,
                perferMedidation: formData.perferMedidation,
                preferPrayer: formData.preferPrayer,
                locationSharing: formData.locationSharing,
            });

            if (imageFile) {
                await updateUserPicture(imageFile);
            }

            router.push("/account");
        } catch (error) {
            console.error("❌ Error updating profile:", error);
            setError(error.response?.data?.message || "An error occurred. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="max-w-2xl mx-auto mt-10 p-6 bg-white shadow-md rounded-lg">
            <h1 className="text-2xl font-bold mb-4 text-black">Edit Profile</h1>
            {error && <p className="text-red-500 text-sm mb-4">{error}</p>}
            <form onSubmit={handleSubmit} className="space-y-4">
                <input type="text" name="firstName" value={formData.firstName} onChange={handleChange} placeholder="First Name" className="input text-black border" required />
                <input type="text" name="lastName" value={formData.lastName} onChange={handleChange} placeholder="Last Name" className="input text-black border" required />
                <input type="text" name="phoneNumber" value={formData.phoneNumber} onChange={handleChange} placeholder="Phone Number" className="input text-black border" />
                <input type="text" name="countryCode" value={formData.countryCode} onChange={handleChange} placeholder="Country Code" className="input text-black border" required />
                <input type="text" name="countryFlagCode" value={formData.countryFlagCode} onChange={handleChange} placeholder="Country Flag Code" className="input text-black border" />

                <div className="space-y-2">
                    <label className="block text-sm font-medium text-black">Profile Picture</label>
                    <input type="file" onChange={handleImageChange} className="input text-black border" />
                </div>

                <div className="space-y-2">
                    <h2 className="font-semibold text-black">Preferences</h2>
                    {[
                        ["pushNotificationStatus", "Push Notifications"],
                        ["communityNotificationStatus", "Community Notifications"],
                        ["wishesNotificationStatus", "Wishes Notifications"],
                        ["gratitudeNotificationStatus", "Gratitude Notifications"],
                        ["perferMedidation", "Prefer Meditation"],
                        ["preferPrayer", "Prefer Prayer"],
                        ["locationSharing", "Share Location"],
                    ].map(([key, label]) => (
                        <label key={key} className="flex items-center space-x-2 text-black">
                            <input
                                type="checkbox"
                                name={key}
                                checked={formData[key]}
                                onChange={handleChange}
                            />
                            <span>{label}</span>
                        </label>
                    ))}
                </div>

                <button type="submit" className="btn-primary text-black border" disabled={loading}>
                    {loading ? "Saving..." : "Save Changes"}
                </button>
            </form>
        </div>
    );
}
