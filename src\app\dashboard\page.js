'use client';

import { useRouter } from "next/navigation";
import { useAuth } from "../../context/UserProvider";
import { useWishes } from '@/context/WishProvider';
import { useEffect, useState } from "react";
import getImageUrl from "@/services/imageURL";
import Divider from "@/components/Divider";
import UnderwaterBackground from "@/components/UnderwaterBackground";
import sleep from "@/utils/sleep";
import Button from "@/components/Button";


export default function Dashboard() {
    const router = useRouter();
    const { user, userProfile, logout } = useAuth();
    const [profileImageUrl, setProfileImageUrl] = useState(null);
    const [leaveAnimation, setLeaveAnimation] = useState(false)
    const [wishesToDisplay, setWishesToDisplay] = useState([])
    const { fetchPinnedWishes, pinnedWishes } = useWishes();
    const [activeTab, setActiveTab] = useState("Favorite");
    // Missing data from providers
    const suggestedWishes = []
    const leastBlessedWishes = []
    const totalUserBlessings = 1
    const wishPositions = [
        "top-[0cqw] left-[41.3cqw]",
        "top-[15.5cqw] left-[71cqw]",
        "top-[45cqw] left-[80cqw]",
        "top-[77cqw] left-[68cqw]",
        "top-[86cqw] left-[35cqw]",
        "top-[71cqw] left-[5cqw]",
        "top-[38cqw] left-[-4.5cqw]",
        "top-[10cqw] left-[10.5cqw]",
    ]
    useEffect(() => {
        const fetchData = async () => {
            await fetchPinnedWishes();
            setWishesToDisplay(pinnedWishes || []);
        };
        fetchData();
    }, []);


    useEffect(() => {
        if (userProfile?.data?.profilePicture) {
            const processedUrl = getImageUrl(userProfile.data.profilePicture);
            setProfileImageUrl(processedUrl);
        }
    }, [userProfile]);

    const handleLeave = async (callback) => {
        setLeaveAnimation(true)
        await sleep(520)
        callback()
    }

    const handleWishesTabChange = (tab) => {
        setActiveTab(tab)
        switch (tab) {
            case "Favorite":
                setWishesToDisplay(pinnedWishes)
                break;
            case "Suggested":
                setWishesToDisplay(suggestedWishes)
                break;
            case "Least Blessed":
                setWishesToDisplay(leastBlessedWishes)
        }
    }

    const toPercent = n => n <= 0 ? 10 : n >= 60 ? 100 : 10 + (80 * n / 60);
    // ^ this function returns a percentage of the number between 0 and 60 -> 20% and 100%
    return (
        <>
            <UnderwaterBackground />
            <div className={`md:py-17 h-full overflow-x-hidden overflow-y-auto opacity-0 animate-fade-in-blur ${leaveAnimation ? 'animate-slide-left-out overflow-hidden' : 'overflow-y-auto'}`}>
                <div className="p-6 max-w-3xl relative pb-20 md:pb-auto min-h-full md:min-h-auto md:mt-10 mx-auto shadow-md bg-transparent md:bg-beige md:rounded-2xl">
                    <div className="hidden md:block ">
                        <h1 className="text-5xl mt-4 mb-8 font-heading text-center text-darkgreen">Dashboard</h1>
                        <Divider />
                    </div>
                    <div className="grid grid-cols-[50px_auto_50px] pb-5 items-center md:hidden">
                        <div>
                            <img src="/img/logo-color.png" width="50px" alt="Wish Well logo." />
                        </div>
                        <div className="pl-4 text-darkgreen">
                            <p className="text-[17px]">Hello</p>
                            <p className="text-[23px] font-bold leading-[1.1em]">{user?.firstName} {user?.lastName}</p>
                        </div>
                        <div className="text-right" onClick={() => handleLeave(() => router.push("/account"))}>
                            {profileImageUrl && <img src={profileImageUrl} className="w-10 h-10 inline-block rounded-full object-cover border border-gray-300" alt="Profile image" />}
                        </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 md:gap-8 space-evenly">
                        <div className="relative h-[120px]   rounded-2xl overflow-hidden">
                            <img src="/img/bg-rectangle-graphic-1.png" alt="Background Graphic" className="absolute top-0 left-0 w-full h-full object-cover" />
                            <div className="relative h-full grid items-end justify-start text-white p-[20px]">
                                <div>
                                    <p className="text-[16px] md:text-[24px]">126 Hrs</p>
                                    <p className="text-[12px] md:text-[20px] font-bold">Blessings By Me</p>
                                </div>
                            </div>
                        </div>
                        <div className="relative h-[120px] rounded-2xl overflow-hidden">
                            <img src="/img/bg-rectangle-graphic-2.png" alt="Background Graphic" className="absolute top-0 left-0 w-full h-full object-cover" />
                            <div className="relative h-full grid items-end justify-start text-white p-[20px]">
                                <div>
                                    <p className="text-[16px] md:text-[24px]">560</p>
                                    <p className="text-[12px] md:text-[20px] font-bold">Gratitude Received</p>
                                </div>
                            </div>
                        </div>
                        <div className="relative h-[120px] rounded-2xl overflow-hidden">
                            <img src="/img/bg-rectangle-graphic-3.png" alt="Background Graphic" className="absolute top-0 left-0 w-full h-full object-cover" />
                            <div className="relative h-full grid items-end justify-start text-white p-[20px]">
                                <div>
                                    <p className="text-[16px] md:text-[24px]">654</p>
                                    <p className="text-[12px] md:text-[20px] font-bold">Blessings Received</p>
                                </div>
                            </div>
                        </div>
                        <div className="relative h-[120px] rounded-2xl overflow-hidden">
                            <img src="/img/bg-rectangle-graphic-4.png" alt="Background Graphic" className="absolute top-0 left-0 w-full h-full object-cover" />
                            <div className="relative h-full grid items-end justify-start text-white p-[20px]">
                                <div>
                                    <p className="text-[16px] md:text-[24px]">234</p>
                                    <p className="text-[12px] md:text-[20px] font-bold">Gratitude Shared</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="flex gap-4 my-6">
                        {["Favorite", "Suggested", "Least Blessed"].map((tab) => (
                            <button
                                key={tab}
                                onClick={() => handleWishesTabChange(tab)}
                                className={`px-3 py-2 rounded-2xl cursor-pointer transition-all duration-200 text-sm ${activeTab === tab
                                    ? 'bg-gradient-to-r from-darkorange to-orange text-white'
                                    : 'bg-gradient-to-r from-darkorange/75 to-orange text-white opacity-70'
                                    }`}
                            >
                                {tab}
                            </button>
                        ))}
                    </div>

                    <div className="@container relative px-2 md:px-0 my-4 mx-2 min-h-[100cqw] md:min-h-[430px] mt-10 md:mt-10 w-full md:mx-auto md:w-[400px] ">
                        {[0, 1, 2, 3, 4, 5, 6, 7].map((index) => (
                            <div key={index} className={`absolute z-1 h-[79px] w-[79px] cursor-pointer ${wishPositions[index]}`}>
                                {wishesToDisplay?.[index]?.wishDetail.image ? (
                                    <img
                                        src={getImageUrl(wishesToDisplay[index].wishDetail.image)}
                                        alt="Wish"
                                        className="rounded-full object-cover h-[79px] w-[79px]"
                                        onClick={() => router.push(`/wishes/${wishesToDisplay[index].wishDetaill._id}`)}
                                    />
                                ) : (
                                    <div className="rounded-full h-[79px] w-[79px] grid items-center justify-center font-bold text-3xl leading-none bg-lightgreen text-darkgreen">+</div>
                                )}
                                {wishesToDisplay?.[index]?.wishDetail.totalBlessings && (
                                    <div className="absolute -top-[8px] right-[4px] w-[32px] cursor-pointer aspect-square font-bold text-[12px] text-darkgreen rounded-full bg-white grid items-center justify-center">
                                        <span >{wishesToDisplay?.[index]?.wishDetail.totalBlessings}</span>
                                    </div>
                                )}
                            </div>
                        ))}
                        <div className="h-[110cqw] w-full grid justify-center items-center relative">
                            <img src="/img/logo-color.png" className="absolute left-0 top-0 object-contain h-full w-full" alt="Wishwell logo"
                                style={{ opacity: `${toPercent(totalUserBlessings)}%` }}
                            />
                            <p className="relative text-[69px] pr-[2cqw] text-darkblue">{totalUserBlessings}</p>
                        </div>
                    </div>

                    <pre className="bg-white hidden p-4 rounded shadow-md w-full overflow-x-auto text-black">
                        {JSON.stringify(pinnedWishes, null, 2)}
                    </pre>

                    <div className="text-center mb-5">
                        <Button
                            size="small"
                            onClick={() => router.push("/communities")}
                        >
                            Communities
                        </Button>
                    </div>
                </div>
            </div>
        </>
    );
}