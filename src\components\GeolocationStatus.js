import { useEffect, useState } from "react";
import Button from "./Button";

export default function GeolocationStatus() {
  const [status, setStatus] = useState("Checking...");

  const updatePermission = () => {
    navigator.permissions?.query({ name: "geolocation" }).then(result => {
      const states = {
        granted: "Geolocation is enabled ✅",
        denied: "Geolocation is disabled ❌",
        prompt: "Geolocation permission not decided ⚠️"
      };
      setStatus(states[result.state]);
    });
  };

  const requestGeolocation = () => {
    navigator.geolocation.getCurrentPosition(
      () => updatePermission(),
      () => updatePermission()
    );
  };

  useEffect(() => {
    updatePermission();
  }, []);
  if (!status.includes("enabled")) {
    return (<div>
      <p className="text-yellow-600 text-center text-[14px] mb-2">{status}</p>
      <Button type="button" onClick={requestGeolocation}>Enable Geolocation</Button>
    </div>)
  }
};