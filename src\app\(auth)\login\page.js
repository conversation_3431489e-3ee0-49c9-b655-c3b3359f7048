'use client';
import { useState } from "react";
import { useRouter } from "next/navigation";
import { loginUser } from "../../../services/api";
import { useAuth } from "../../../context/UserProvider";
import AuthFrame from "@/components/AuthFrame";
import TextInput from "@/components/TextInput";
import Link from "@/components/Link";
import Button from "@/components/Button";
import LoadingIndicator from "@/components/LoadingIndicator";
import BackButton from "@/components/BackButton";

export default function LoginPage() {
    const router = useRouter();
    const { login, signInWithGoogle, signInWithApple } = useAuth();
    const [formData, setFormData] = useState({ email: "", password: "" });
    const [error, setError] = useState("");
    const [loading, setLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError("");
        setLoading(true);

        try {
            const data = await loginUser(formData);
            console.log("Login response:", data);
            if (data && data.data.accessToken) {
                login(data.data, data.data.accessToken);
            }
        } catch (err) {
            setError(err.message || "Login failed");
        } finally {
            setLoading(false);
        }
    };

    return (
        <AuthFrame>
            <div className="relative">
                <BackButton
                    className="absolute left-2 top-1"
                    onClick={() => router.push("/signup")}
                />
                <img src="/img/logo-color.png" width="64" height="auto" alt="Wish Well logo" className="mx-auto mb-20" />
                <form onSubmit={handleSubmit} className="grid gap-6">
                    <TextInput
                        type="email"
                        name="email"
                        placeholder="Email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                    />

                    <div className="relative w-full">
                        <TextInput
                            type={showPassword ? "text" : "password"}
                            name="password"
                            placeholder="Password"
                            value={formData.password}
                            onChange={handleChange}
                            required
                        />
                        <button
                            type="button"
                            className="absolute right-3 top-4 cursor-pointer opacity-80"
                            onClick={() => setShowPassword(!showPassword)}
                        >
                            <img width="20" src={showPassword ? "/img/eye.svg" : "/img/eye-closed.svg"}
                                alt={showPassword ? "Hide password button" : "Show password button"}

                            />
                        </button>
                        <p className={`text-error text-[13px] mt-2 mb-4  transition-all duration-200 ${error ? 'opacity-100' : 'opacity-0'}`}>
                            <img src="/img/error-icon.svg" width="12px" className="inline-block align-middle mr-1" alt="Error icon." />
                            {error}
                        </p>
                    </div>

                    <Button
                        type="submit"
                        className={`${formData.password && formData.email ? '' : 'opacity-50 pointer-events-none'}`}
                        disabled={loading || !formData.email || !formData.password}
                    >
                        {loading ? <LoadingIndicator /> : "Login"}

                    </Button>
                    <Button
                        className={`bg-black text-white grid grid-cols-[auto_auto] gap-2 justify-center items-center mb-6`}
                        onClick={() => signInWithApple({ login, onError: (err) => setError(err.message) })}
                        disabled={loading}
                    >
                        <img src="/img/apple-logo.svg" width="15px" className="mt-[1px]" alt="Apple icon." />
                        <span>Login with Apple</span>
                    </Button>
                    <Button
                        className={`bg-white shadow-md text-white font-normal grid grid-cols-[auto_auto] gap-2 justify-center items-center`}
                        onClick={() => signInWithGoogle({ login, onError: (err) => setError(err.message || "Google sign-in failed.") })}
                        disabled={loading}
                    >
                        <img src="/img/google-logo.svg" width="15px" className="mt-[1px]" alt="Google icon." />
                        <span>Login with Google</span>
                    </Button>
                    <p className="mt-8">
                        Forgot Password?
                        <Link
                            onClick={() => router.push("/forgot-password")}
                            className="ml-1 font-bold"
                        >
                            Reset
                        </Link>
                    </p>
                    <p className="mt-2">
                        Want to join?
                        <Link
                            onClick={() => router.push("/signup")}
                            className="ml-1 font-bold"
                        >
                            Sign Up
                        </Link>
                    </p>
                </form>
            </div>
        </AuthFrame>
    );
}