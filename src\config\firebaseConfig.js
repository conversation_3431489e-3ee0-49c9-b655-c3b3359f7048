import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";

const firebaseConfig = {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
    measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

let messaging;
if (typeof window !== "undefined") {
    try {
        const { getMessaging, isSupported } = require("firebase/messaging");
        isSupported().then((supported) => {
            if (supported) {
                messaging = getMessaging(app);
            } else {
                console.warn("FCM is not supported in this browser.");
            }
        });
    } catch (error) {
        console.warn("Messaging setup skipped:", error.message);
    }
}


export { app, auth, messaging };
