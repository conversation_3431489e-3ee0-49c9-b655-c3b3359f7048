import { useRef, useState } from "react"
import { useOutsideClick } from "@/utils/useOuterClick";
export default function SelectInput({ value = "", className = "", placeholder = "Select", options = [], handleSelect, ...props }) {
    const [isOpen, setIsOpen] = useState(false)
    const optionsRef = useRef(null)
    useOutsideClick(optionsRef.current, () => {
        if (isOpen) setIsOpen(false)
    }, [isOpen]);
    const optionName = options?.find((o) => o.value === value)?.name
    return (<div className="relative">
        <input
            value={optionName || value?.toLowerCase() || ""}
            readOnly
            className={`relative w-full cursor-pointer capitalize py-3 px-4 border rounded-2xl border-orange focus-visible:bg-beige focus-visible:outline-1 outline-orange transition-colors duration-200 ${className}`}
            onClick={() => setIsOpen(true)}
            {...props}
        />
        <svg className={`w-6 h-6 absolute cursor-pointer top-3 right-2 transition-all duration-200 ${isOpen ? 'rotate-180' : ''} text-darkblue`} xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path d="M6 9l6 6 6-6" />
        </svg>
        <div className={`absolute left-4 focus-visible:top-[2px] top-3 text-gray-700 focus-visible:left-5 ${value ? 'hidden' : ''} transition-all duration-200`}>{placeholder}</div>
        <div ref={optionsRef} className={`absolute z-10 top-15 overflow-y-auto max-h-[300px] left-0 rounded-2xl bg-white w-full items-start grid gap-4 p-3 shadow-lg transition-all duration-200 ${isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}>
            {options.map((option, index) => {
                return (<div key={index} onClick={() => { handleSelect(option.value); setIsOpen(false) }}
                    className={`px-4 py-2 rounded-2xl cursor-pointer font-semibold transition-all duration-300 text-sm capitalize ${value === option.value
                        ? 'bg-gradient-to-r from-darkblue to-lightblue text-white'
                        : 'bg-linear-65 from-beige to-orange/50 text-gray-700'
                        }`}
                > {option.name}</div>)
            })}
        </div>
    </div >)
}