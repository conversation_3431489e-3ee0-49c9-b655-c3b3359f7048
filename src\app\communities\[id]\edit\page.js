'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useCommunities } from '@/context/CommunityProvider';
import getImageUrl from '@/services/imageURL';

export default function EditCommunityPage() {
    const { id } = useParams();
    const router = useRouter();
    const { communities, updateCommunityById } = useCommunities();
    const [communityId, setCommunityId] = useState('');

    const [form, setForm] = useState({
        name: '',
        purpose: '',
        image: '',
    });

    const [loading, setLoading] = useState(false);
    const [selectedImageFile, setSelectedImageFile] = useState(null);
    const [error, setError] = useState('');

    useEffect(() => {
        const found = communities?.data?.find((c) => c.communityDetail._id === id);
        if (found) {
            setCommunityId(found.communityDetail._id); // 💡 Save it to use in submit
            setForm({
                name: found.communityDetail.name || '',
                purpose: found.communityDetail.purpose || '',
                image: found.communityDetail.image || '',
            });
        }
    }, [id, communities]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setForm((prev) => ({ ...prev, [name]: value }));
    };

    const handleImageUpload = (e) => {
        const file = e.target.files[0];
        if (file) {
            if (form.image.startsWith('blob:')) {
                URL.revokeObjectURL(form.image); // cleanup old preview
            }
            setSelectedImageFile(file);
            const previewUrl = URL.createObjectURL(file);
            setForm((prev) => ({ ...prev, image: previewUrl }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');
        try {
            await updateCommunityById(
                communityId,
                {
                    name: form.name,
                    purpose: form.purpose,
                },
                selectedImageFile
            );
            router.push('/communities');
        } catch (err) {
            console.error(err);
            setError('Failed to update community');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="max-w-xl mx-auto p-6 mt-10 bg-white rounded shadow">
            <h1 className="text-2xl font-bold mb-4">Edit Community</h1>

            {error && <p className="text-red-500 mb-4">{error}</p>}

            <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                    <label className="block font-medium mb-1">Name</label>
                    <input
                        type="text"
                        name="name"
                        value={form.name}
                        onChange={handleChange}
                        className="w-full border px-3 py-2 rounded"
                        required
                    />
                </div>

                <div>
                    <label className="block font-medium mb-1">Purpose</label>
                    <input
                        type="text"
                        name="purpose"
                        value={form.purpose}
                        onChange={handleChange}
                        className="w-full border px-3 py-2 rounded"
                    />
                </div>

                <div>
                    <label className="block font-medium mb-1">Upload New Image</label>
                    <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="block"
                    />
                </div>

                {form.image && (
                    <div className="mt-4">
                        <label className="block font-medium mb-1">Preview</label>
                        <img
                            src={
                                form.image.startsWith('blob:')
                                    ? form.image
                                    : getImageUrl(form.image)
                            }
                            alt="Community"
                            className="w-24 h-24 rounded-full object-cover border"
                        />
                    </div>
                )}

                <button
                    type="submit"
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                    disabled={loading}
                >
                    {loading ? 'Saving...' : 'Save Changes'}
                </button>
            </form>
        </div>
    );
}
