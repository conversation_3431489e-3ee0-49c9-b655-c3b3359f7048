import { useEffect } from "react";

export const useGeolocationPermission = (onChange) => {
  useEffect(() => {
    let permissionStatus;

    navigator.permissions?.query({ name: "geolocation" }).then(status => {
      permissionStatus = status;
      onChange(status.state);
      status.onchange = () => onChange(status.state);
    });

    return () => {
      if (permissionStatus) permissionStatus.onchange = null;
    };
  }, [onChange]);
};