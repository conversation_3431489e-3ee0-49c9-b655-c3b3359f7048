'use client';

import { createContext, useContext, useState } from 'react';
import { usePathname, useSearchParams, useRouter } from 'next/navigation'
const LayoutContext = createContext();

export const LayoutProvider = ({ children }) => {
    const [showBottomNav, setShowBottomNav] = useState(true);
    const pathname = usePathname()
    let initialActiveTab = pathname.split("/")[1];
    const [activeTab, setActiveTab] = useState(initialActiveTab || "dashboard");
    const value = {
        showBottomNav,
        setShowBottomNav,
        activeTab,
        setActiveTab
    };

    return (
        <LayoutContext.Provider value={value}>
            {children}
        </LayoutContext.Provider>
    );
};

export const useLayout = () => {
    const context = useContext(LayoutContext);
    if (!context) {
        throw new Error('useLayout must be used within a LayoutProvider');
    }
    return context;
};