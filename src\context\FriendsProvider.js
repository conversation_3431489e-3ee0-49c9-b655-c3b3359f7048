'use client';
//vercel push
import { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { 
  getFriends, 
  getFriendRequests, 
  sendFriendRequest, 
  respondToFriendRequest 
} from '@/services/api';
import { useAuth } from './UserProvider';

const FriendsContext = createContext();

export const FriendsProvider = ({ children }) => {
  const { accessToken, user } = useAuth();
  
  const [friends, setFriends] = useState([]);
  const [friendRequests, setFriendRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchFriends = useCallback(async (skip = 0, limit = 20) => {
    if (!accessToken) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await getFriends(accessToken, skip, limit);
      setFriends(response.data || []);
    } catch (err) {
      console.error('❌ Error fetching friends:', err);
      setError(err.message || 'Failed to fetch friends');
    } finally {
      setLoading(false);
    }
  }, [accessToken]);

  const fetchFriendRequests = useCallback(async (skip = 0, limit = 20) => {
    if (!accessToken) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await getFriendRequests(accessToken, skip, limit);
      setFriendRequests(response.data || []);
    } catch (err) {
      console.error('❌ Error fetching friend requests:', err);
      setError(err.message || 'Failed to fetch friend requests');
    } finally {
      setLoading(false);
    }
  }, [accessToken]);

  const sendRequest = useCallback(async (userId) => {
    if (!accessToken) return;
    
    setLoading(true);
    setError(null);
    
    try {
      await sendFriendRequest(userId, accessToken);
      // Refresh friend requests after sending
      await fetchFriendRequests();
      return true;
    } catch (err) {
      console.error('❌ Error sending friend request:', err);
      setError(err.message || 'Failed to send friend request');
      return false;
    } finally {
      setLoading(false);
    }
  }, [accessToken, fetchFriendRequests]);

  const respondToRequest = useCallback(async (requestId, accept) => {
    if (!accessToken) return;
    
    setLoading(true);
    setError(null);
    
    try {
      await respondToFriendRequest(requestId, accept, accessToken);
      // Refresh both lists after responding
      await Promise.all([fetchFriendRequests(), fetchFriends()]);
      return true;
    } catch (err) {
      console.error('❌ Error responding to friend request:', err);
      setError(err.message || 'Failed to respond to friend request');
      return false;
    } finally {
      setLoading(false);
    }
  }, [accessToken, fetchFriendRequests, fetchFriends]);

  useEffect(() => {
    if (accessToken) {
      fetchFriends();
      fetchFriendRequests();
    }
  }, [accessToken, fetchFriends, fetchFriendRequests]);

  return (
    <FriendsContext.Provider
      value={{
        friends,
        friendRequests,
        loading,
        error,
        sendRequest,
        respondToRequest,
        fetchFriends,
        fetchFriendRequests
      }}
    >
      {children}
    </FriendsContext.Provider>
  );
};

export const useFriends = () => useContext(FriendsContext);