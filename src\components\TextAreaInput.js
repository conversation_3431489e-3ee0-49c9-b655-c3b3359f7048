export default function TextAreaInput({ className = "", placeholder = "", maxLength = "", value, children, ...props }) {
    return (<div className="relative">
        <textarea
            {...props}
            value={value}
            maxLength={maxLength}
            className={`relative w-full py-3 px-4 border rounded-2xl border-orange focus-visible:bg-beige focus-visible:outline-1 outline-orange transition-colors duration-200 ${className}`}
        />
        <div className={`absolute left-4 focus-visible:top-[2px] focus-visible:left-5 ${value ? 'hidden' : 'top-3 text-gray-700'} transition-all duration-200 pointer-events-none`}>{placeholder}</div>
        {maxLength && <p className="font-medium text-[12px] text-right text-lightblue">{value?.length || 0}/{maxLength} characters left</p>}
    </div>)
}