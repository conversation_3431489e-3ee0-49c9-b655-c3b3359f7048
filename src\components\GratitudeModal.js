"use client";

import { useState, useEffect } from "react";
import { <PERSON>alog, DialogPanel, DialogTitle } from "@headlessui/react";
import { uploadMedia } from "@/services/api";
import { useBlessings } from "@/context/BlessingsProvider";

export default function GratitudeModal({ isOpen, onClose, blessingId, wishId, gratitudeType }) {
    const { giveGratitude } = useBlessings();
    const [localBlessingId, setLocalBlessingId] = useState(null);
    const [localWishId, setLocalWishId] = useState(null);
    
    const [localNotes, setLocalNotes] = useState("");
    
    const [mediaRecorder, setMediaRecorder] = useState(null);
    const [recording, setRecording] = useState(false);
    const [audioBlob, setAudioBlob] = useState(null);
    const [audioURL, setAudioURL] = useState(null);
    const [recordingTimeoutId, setRecordingTimeoutId] = useState(null);
    const [secondsLeft, setSecondsLeft] = useState(60);
    const [countdownIntervalId, setCountdownIntervalId] = useState(null);



    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitSuccess, setSubmitSuccess] = useState(false);
    const [submitError, setSubmitError] = useState(null);

    useEffect(() => {
        if (isOpen) {
            setLocalBlessingId(blessingId);
            setLocalWishId(wishId);
        }
        if (!isOpen) {
            setRecording(false);
            setSecondsLeft(60);
            clearTimeout(recordingTimeoutId);
            clearInterval(countdownIntervalId);
        }
    }, [isOpen, blessingId, wishId]);

    const startRecording = async () => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            const recorder = new MediaRecorder(stream);
            const chunks = [];
    
            recorder.ondataavailable = (e) => chunks.push(e.data);
            recorder.onstop = () => {
                const blob = new Blob(chunks, { type: 'audio/webm' });
                if (blob.size > 5 * 1024 * 1024) { // optional fallback for ~5MB max
                    alert("Recording is too large. Please keep it under 60 seconds.");
                    return;
                }
                setAudioBlob(blob);
                setAudioURL(URL.createObjectURL(blob));
            };
    
            recorder.start();
            setMediaRecorder(recorder);
            setRecording(true);

            setSecondsLeft(60);
            const countdown = setInterval(() => {
                setSecondsLeft(prev => {
                    if (prev <= 1) {
                        clearInterval(countdown);
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);
    
            // Auto-stop after 60s
            const timeoutId = setTimeout(() => {
                recorder.stop();
                setRecording(false);
            }, 60000); // 60s
    
            setRecordingTimeoutId(timeoutId);
            setCountdownIntervalId(countdown);
        } catch (err) {
            console.error("🎤 Recording error:", err);
            alert("Microphone access denied or not available.");
        }
    };
    
    const stopRecording = () => {
        if (recordingTimeoutId) {
            clearTimeout(recordingTimeoutId);
            setRecordingTimeoutId(null);
        }
        mediaRecorder?.stop();
        if (countdownIntervalId) {
            clearInterval(countdownIntervalId);
            setCountdownIntervalId(null);
        }
        setSecondsLeft(60);
        setRecording(false);
    };

    const handleGiveGratitude = async () => {
        if (isSubmitting || submitSuccess) return;
        if (!localBlessingId || !localWishId) {
            console.warn("⛔ Cannot give gratitude: missing blessing or wishId");
            return;
        }
        let voiceNoteURL = null

        setIsSubmitting(true);
        setSubmitError(null);

        try {
            console.log("🔄 Giving gratitude from Modal with IDs:", localWishId, localBlessingId);
            if (audioBlob) {
                console.log("🎤 Uploading audio blob:", audioBlob);
                const audioFile = new File([audioBlob], `gratitude-${Date.now()}.webm`, { type: 'audio/webm' });
                voiceNoteURL = await uploadMedia(audioFile);
                console.log("🎤 Audio uploaded successfully:", voiceNoteURL);
            }
            const gratitude = await giveGratitude({ wishId: localWishId, blessingId: localBlessingId, notes: localNotes, gratitudeType, audioURL: voiceNoteURL });
            console.log("✅ Gratitude given successfully:", gratitude);
            setSubmitSuccess(true);
        } catch (err) {
            console.error("❌ Error performing blessing:", err);
            setSubmitError("Something went wrong. Please try again.");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Dialog open={isOpen} onClose={onClose} className="relative z-50">
            <div className="fixed inset-0 bg-black/50" aria-hidden="true" />

            <div className="fixed inset-0 flex items-center justify-center p-4">
                <DialogPanel className="relative bg-white rounded-2xl shadow-xl max-w-md w-full p-6">
                    <DialogTitle className="text-xl font-bold mb-2">Give Gratitude</DialogTitle>

                    <p className="text-sm text-gray-600 mb-2">Notes:</p>
                    <textarea
                        value={localNotes}
                        onChange={(e) => setLocalNotes(e.target.value)}
                        className="w-full h-24 p-2 border rounded-lg mb-4"
                        placeholder="Add any notes or thoughts here..."
                    />

                    <div className="mb-4">
                        {audioURL ? (
                            <div className="mb-2">
                                <audio controls src={audioURL} className="w-full" />
                                <button onClick={() => { setAudioBlob(null); setAudioURL(null); }} className="text-red-500 text-sm">Clear recording</button>
                            </div>
                        ) : (
                            <button
                                onClick={recording ? stopRecording : startRecording}
                                className={`w-full ${recording ? "bg-red-600" : "bg-green-600"} text-white py-2 rounded-lg mb-2`}
                            >
                                {recording ? "Stop Recording" : "Start Recording"}
                            </button>
                        )}
                        {recording && (
                            <p className="text-center text-sm text-gray-600">
                                Time remaining: {secondsLeft}s
                            </p>
                        )}
                    </div>

                    {submitSuccess ? (
                        <p className="text-green-600 text-center font-semibold">Gratitude given! 🙏</p>
                    ) : (
                        <button
                            onClick={handleGiveGratitude}
                            disabled={isSubmitting}
                            className="w-full bg-blue-600 text-white py-2 rounded-lg"
                        >
                            {isSubmitting ? "Submitting..." : "Give Gratitude"}
                        </button>
                    )}

                    {submitError && <p className="text-red-600 text-sm mt-2 text-center">{submitError}</p>}

                    <button
                        onClick={onClose}
                        className="absolute top-3 right-4 text-gray-500 hover:text-black text-lg"
                    >
                        ✕
                    </button>
                </DialogPanel>
            </div>
        </Dialog>
    );
}
