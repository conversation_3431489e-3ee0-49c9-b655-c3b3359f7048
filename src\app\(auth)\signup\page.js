'use client';
import { useState } from "react";
import { useRouter } from "next/navigation";
import { signupUser } from "../../../services/api";
import { useAuth } from "../../../context/UserProvider";
import LoadingIndicator from "@/components/LoadingIndicator";
import Divider from "@/components/Divider";
import Button from "@/components/Button";
import Link from "@/components/Link";
import TextInput from "@/components/TextInput";
import AuthFrame from "@/components/AuthFrame";

export default function SignupPage() {
    const router = useRouter();
    const {login, signInWithGoogle, signInWithApple } = useAuth();
    const [email, setEmail] = useState("");
    const [confirmEmail, setConfirmEmail] = useState("");
    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [error, setError] = useState("");
    const [loading, setLoading] = useState(false);

    const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");

    // Front-end validation
    if (!email || !password) {
        setError("Email and password are required.");
        return;
    }

    if (email !== confirmEmail) {
        setError("Emails do not match.");
        return;
    }

    if (password !== confirmPassword) {
        setError("Passwords do not match.");
        return;
    }

    if (password.length < 8 || !/\d/.test(password) || !/[a-zA-Z]/.test(password)) {
        setError("Password must be at least 8 characters and include letters and numbers.");
        return;
    }

    setLoading(true);

    try {
        const result = await signupUser({ email, password }); // fields match backend
        if (result?.data?.accessToken) {
            login(result.data, result.data.accessToken); // save to auth context
            router.push("/"); // redirect to home or dashboard
        } else {
            throw new Error("Invalid response from server");
        }
    } catch (err) {
        console.error("Signup error:", err);
        setError(err.message || "Signup failed.");
    } finally {
        setLoading(false);
    }
};

    return (
        <AuthFrame>
            <div>
                <img src="/img/logo-color.png" width="64" height="auto" alt="Wish Well logo" className="mx-auto" />
                <h2 className="font-subheading pt-4 pb-12 text-[16px]">Sign up to enjoy<br />
                    the full experience. </h2>
                <form onSubmit={handleSubmit} className="grid gap-6">
                    <TextInput
                        type="email"
                        value={email}
                        onChange={(e) => { setEmail(e.target.value); setError("") }}
                        placeholder="Email"
                    />
                    <TextInput
                        type="email"
                        value={confirmEmail}
                        onChange={(e) => { setConfirmEmail(e.target.value); setError("") }}
                        placeholder="Confirm Email"
                    />
                    <div className="relative w-full">
                        <TextInput
                            type={showPassword ? "text" : "password"}
                            name="password"
                            placeholder="Password"
                            value={password}
                            onChange={(e) => { setPassword(e.target.value); setError("") }}
                            required
                        />
                        <button
                            type="button"
                            className="absolute right-3 top-4 cursor-pointer opacity-80"
                            onClick={() => setShowPassword(!showPassword)}
                        >
                            <img width="20" src={showPassword ? "/img/eye.svg" : "/img/eye-closed.svg"}
                                alt={showPassword ? "Hide password button" : "Show password button"}

                            />
                        </button>
                    </div>
                    <div className="relative w-full">
                        <TextInput
                            type={showConfirmPassword ? "text" : "password"}
                            name="confirmPassword"
                            placeholder="Confirm Password"
                            value={confirmPassword}
                            onChange={(e) => { setConfirmPassword(e.target.value); setError("") }}
                            required
                        />
                        <button
                            type="button"
                            className="absolute right-3 top-4 cursor-pointer opacity-80"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                            <img width="20" src={showConfirmPassword ? "/img/eye.svg" : "/img/eye-closed.svg"}
                                alt={showConfirmPassword ? "Hide password button" : "Show password button"}
                            />
                        </button>
                        <p className="text-[13px] mt-2 mb-4">Password must be at least 8 characters long and contain at least one letter and one number.</p>
                    </div>
                    <p className={`text-error text-[13px] mt-2 mb-4  transition-all duration-200 ${error ? 'opacity-100' : 'opacity-0'}`}>
                        <img src="/img/error-icon.svg" width="12px" className="inline-block align-middle mr-1" alt="Error icon." />
                        {error}
                    </p>
                    <Button
                        type="submit"
                        className={`${email ? '' : 'opacity-50 pointer-events-none'}`}
                        disabled={loading || !email}
                    >
                        {loading ? <LoadingIndicator /> : "Sign Up"}

                    </Button>
                    <Divider className="my-9" />
                </form>
                <Button
                    className={`bg-black text-white grid grid-cols-[auto_auto] gap-2 justify-center items-center mb-6`}
                    onClick={() => signInWithApple({ login, onError: (err) => setError(err.message) })}
                    disabled={loading}
                >
                    <img src="/img/apple-logo.svg" width="15px" className="mt-[1px]" alt="Apple icon." />
                    <span>Sign Up with Apple</span>
                </Button>
                <Button
                    className={`bg-white shadow-md text-white font-normal grid grid-cols-[auto_auto] gap-2 justify-center items-center`}
                    onClick={() => signInWithGoogle({ login, onError: (err) => setError(err.message || "Google sign-in failed.") })}
                    disabled={loading}
                >
                    <img src="/img/google-logo.svg" width="15px" className="mt-[1px]" alt="Google icon." />
                    <span>Sign Up with Google</span>
                </Button>
                <p className="text-[16px] pt-12 pb-12">Already have an account? <Link onClick={() => router.push('/login')} className="font-bold">Sign In</Link></p>

                <p className="text-xs">By signing up you agree to our<br /> <Link onClick={() => router.push('/login')}>Terms of use</Link>, <Link onClick={() => router.push('/login')}>Privacy policy</Link> & <Link onClick={() => router.push('/login')}>Community guidelines</Link>.</p>
            </div>
        </AuthFrame>
    );
}
