import { reportContent as apiReportContent } from '@/services/api';

export async function reportWish({ wishId, reason, description }) {
    return apiReportContent({
        subjectId: wishId,
        reportType: reason,
        description,
        type: 'WISH',
    });
}

export async function reportCommunity({ communityId, reason, description }) {
    return apiReportContent({
        subjectId: communityId,
        reportType: reason,
        description,
        type: 'COMMUNITY',
    });
}

export async function reportBlessing({ blessingId, reason, description }) {
    return apiReportContent({
        subjectId: blessingId,
        reportType: reason,
        description,
        type: 'BLESSING',
    });
}

export async function reportGratitude({ gratitudeId, reason, description }) {
    return apiReportContent({
        subjectId: gratitudeId,
        reportType: reason,
        description,
        type: 'GRATITUDE',
    });
}
