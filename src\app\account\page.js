"use client";

import { useRouter } from "next/navigation";
import { useAuth } from "@/context/UserProvider";
import getImageUrl from "@/services/imageURL"; // 👈 make sure this is the updated function
import { useEffect, useState } from "react";
import LoadingIndicator from "@/components/LoadingIndicator";

export default function ProfilePage() {
    const router = useRouter();
    const { user, userProfile, loading, logout } = useAuth();
    const [profileImageUrl, setProfileImageUrl] = useState(null);
    const [leaveAnimation, setLeaveAnimation] = useState(false)

    useEffect(() => {
        if (userProfile?.data?.profilePicture) {
            const processedUrl = getImageUrl(userProfile.data.profilePicture);
            console.log("✅ Processed profile image URL:", processedUrl);
            setProfileImageUrl(processedUrl);
        }
    }, [userProfile]);

    if (loading) return <div className="min-h-screen grid justify-center items-center"><LoadingIndicator /></div>;

    if (!userProfile) {
        return <p className="text-center mt-10 text-red-500">Profile data not found.</p>;
    }

    return (
        <div className={`md:py-17 h-full overflow-y-auto overflow-x-hidden relative opacity-0 animate-fade-in-blur ${leaveAnimation ? 'animate-slide-left-out' : ''}`}>
            <div className="p-6 max-w-3xl pb-20 md:pb-auto min-h-full md:min-h-auto md:mt-10 mx-auto backdrop-blur-md shadow-md bg-lightbeige md:rounded-2xl">
                <h1 className="text-5xl mt-4 mb-8 font-heading text-center text-darkgreen">Profile</h1>
                <pre className="mt-4 bg-gray-100 p-4 rounded-md text-sm overflow-x-auto text-black">
                    {JSON.stringify(userProfile, null, 2)}
                </pre>
                <pre className="mt-4 bg-gray-100 p-4 rounded-md text-sm overflow-x-auto text-black">
                    {JSON.stringify(user, null, 2)}
                </pre>

                {profileImageUrl && (
                    <div className="mt-4">
                        <h2 className="text-lg font-semibold mb-2 text-black">Profile Picture:</h2>
                        <img
                            src={profileImageUrl}
                            alt="Profile"
                            className="w-40 h-40 rounded-full object-cover border border-gray-300"
                        />
                    </div>
                )}

                <div className="flex flex-wrap gap-4 mt-6 mb-20">
                    <button
                        onClick={() => router.push("/dashboard")}
                        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                    >
                        Go to Dashboard
                    </button>
                    <button
                        onClick={() => router.push('/friends')}
                        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                    >
                        Manage Friends
                    </button>
                    <button
                        onClick={() => router.push("/account/edit")}
                        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                    >
                        Edit Profile
                    </button>
                    <button
                        onClick={logout}
                        className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
                    >
                        Logout
                    </button>
                </div>
            </div>
        </div>
    );
}
