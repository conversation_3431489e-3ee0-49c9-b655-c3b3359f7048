'use client';
import { useFriends } from '@/context/FriendsProvider';
import { useCommunities } from '@/context/CommunityProvider';
import { useAuth } from '@/context/UserProvider';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function CommunityPage({ params }) {
  const router = useRouter();
  const { friends, sendRequest } = useFriends();
  const { user } = useAuth();
  const {
    fetchCommunityDetail,
    currentCommunity,
    loading,
    error,
    leaveCommunityById
  } = useCommunities();

  const [isOwner, setIsOwner] = useState(false);
  const [isMember, setIsMember] = useState(false);
  const [communityMembers, setCommunityMembers] = useState([]);

  // Add this function to check if a user is already a friend
  const isFriend = (userId) => {
    return friends.some(friend => friend._id === userId);
  };

  // Add this function to handle sending friend requests
  const handleSendFriendRequest = async (userId) => {
    await sendRequest(userId);
  };

  // <PERSON>le leaving the community
  const handleLeaveCommunity = async () => {
    if (confirm('Are you sure you want to leave this community?')) {
      try {
        await leaveCommunityById(params.id);
        router.push('/communities');
      } catch (error) {
        console.error('Error leaving community:', error);
      }
    }
  };

  // Fetch community data and members
  useEffect(() => {
    const fetchCommunityData = async () => {
      try {
        if (!params.id) return;

        await fetchCommunityDetail(params.id);
      } catch (error) {
        console.error('Error fetching community data:', error);
      }
    };

    fetchCommunityData();
  }, [params.id, fetchCommunityDetail]);

  // Process community data when it's loaded
  useEffect(() => {
    if (currentCommunity) {
      // Check if user is owner
      setIsOwner(currentCommunity.createdBy?._id === user?._id);

      // Check if user is a member
      const memberIds = (currentCommunity.members || []).map(member => member._id);
      setIsMember(memberIds.includes(user?._id));

      // Set community members
      setCommunityMembers(currentCommunity.members || []);
    }
  }, [currentCommunity, user]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 flex justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p>Error loading community: {error.message || 'Unknown error'}</p>
        </div>
      </div>
    );
  }

  if (!currentCommunity) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
          <p>Community not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <div className="relative h-40 bg-gradient-to-r from-blue-500 to-purple-600">
          {currentCommunity.image && (
            <img
              src={currentCommunity.image}
              alt={currentCommunity.name}
              className="w-full h-full object-cover"
            />
          )}
        </div>

        <div className="p-6">
          <h1 className="text-2xl font-bold mb-2">{currentCommunity.name}</h1>
          <p className="text-gray-600 mb-4">{currentCommunity.purpose}</p>

          <div className="flex items-center text-sm text-gray-500 mb-4">
            <span className="mr-4">
              <span className="font-medium">{communityMembers.length}</span> members
            </span>
            <span>
              Created by <span className="font-medium">{currentCommunity.createdBy?.name || 'Unknown'}</span>
            </span>
          </div>

          {isMember && !isOwner && (
            <button
              onClick={handleLeaveCommunity}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition"
            >
              Leave Community
            </button>
          )}

          {isOwner && (
            <button
              onClick={() => router.push(`/communities/${params.id}/edit`)}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition mr-2"
            >
              Edit Community
            </button>
          )}
        </div>
      </div>

      <h2 className="text-xl font-semibold mb-4">Members</h2>
      {communityMembers.length === 0 ? (
        <p className="text-gray-500">No members found</p>
      ) : (
        <ul className="divide-y divide-gray-200 bg-white rounded-lg shadow-md overflow-hidden">
          {communityMembers.map((member) => (
            <li key={member._id} className="py-4 px-6 flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-300 mr-3 overflow-hidden">
                  {member.profilePicture ? (
                    <img
                      src={member.profilePicture}
                      alt={member.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-10 h-10 flex items-center justify-center bg-blue-100 text-blue-500 font-bold">
                      {member.name?.charAt(0).toUpperCase() || '?'}
                    </div>
                  )}
                </div>
                <div>
                  <p className="font-medium">{member.name}</p>
                  <p className="text-sm text-gray-500">{member.email}</p>
                </div>
              </div>

              {member._id !== user?._id && (
                isFriend(member._id) ? (
                  <span className="text-green-500 font-medium">Friend</span>
                ) : (
                  <button
                    onClick={() => handleSendFriendRequest(member._id)}
                    className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
                  >
                    Add Friend
                  </button>
                )
              )}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}