'use client';

import { useState } from 'react';
import { useFriends } from '@/context/FriendsProvider';
import LoadingIndicator from '@/components/LoadingIndicator';
import Button from '@/components/Button';
import TextInput from '@/components/TextInput';
import BackButton from '@/components/BackButton';
import Divider from '@/components/Divider';

export default function FriendsPage() {
  const { 
    friends, 
    friendRequests, 
    loading, 
    error, 
    sendRequest, 
    respondToRequest 
  } = useFriends();
  
  const [userId, setUserId] = useState('');
  const [activeTab, setActiveTab] = useState('Friends');

  const handleSendRequest = async () => {
    if (!userId.trim()) return;
    await sendRequest(userId.trim());
    setUserId('');
  };

  const handleRespondToRequest = async (requestId, accept) => {
    await respondToRequest(requestId, accept);
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <BackButton />
        <h1 className="text-2xl font-bold ml-4">Friends</h1>
      </div>

      <div className="flex mb-6">
        <button
          className={`flex-1 py-2 ${activeTab === 'Friends' ? 'border-b-2 border-blue-500 font-bold' : ''}`}
          onClick={() => setActiveTab('Friends')}
        >
          Friends ({friends.length})
        </button>
        <button
          className={`flex-1 py-2 ${activeTab === 'Requests' ? 'border-b-2 border-blue-500 font-bold' : ''}`}
          onClick={() => setActiveTab('Requests')}
        >
          Requests ({friendRequests.length})
        </button>
      </div>

      {loading && <LoadingIndicator />}
      
      {error && <p className="text-red-500 mb-4">{error}</p>}

      {activeTab === 'Friends' ? (
        <>
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-4">Add Friend</h2>
            <div className="flex">
              <TextInput
                placeholder="Enter user ID"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                className="flex-1 mr-2"
              />
              <Button onClick={handleSendRequest}>Send Request</Button>
            </div>
          </div>

          <Divider />

          <h2 className="text-xl font-semibold my-4">Your Friends</h2>
          {friends.length === 0 ? (
            <p className="text-gray-500">You don&#39;t have any friends yet.</p>
          ) : (
            <ul className="divide-y">
              {friends.map((friend) => (
                <li key={friend._id} className="py-4 flex items-center">
                  <div className="w-10 h-10 rounded-full bg-gray-300 mr-3">
                    {friend.profilePicture && (
                      <img 
                        src={friend.profilePicture} 
                        alt={friend.name} 
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    )}
                  </div>
                  <div>
                    <p className="font-medium">{friend.name}</p>
                    <p className="text-sm text-gray-500">{friend.email}</p>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </>
      ) : (
        <>
          <h2 className="text-xl font-semibold my-4">Friend Requests</h2>
          {friendRequests.length === 0 ? (
            <p className="text-gray-500">You don&#39;t have any friend requests.</p>
          ) : (
            <ul className="divide-y">
              {friendRequests.map((request) => (
                <li key={request._id} className="py-4">
                  <div className="flex items-center mb-2">
                    <div className="w-10 h-10 rounded-full bg-gray-300 mr-3">
                      {request.from.profilePicture && (
                        <img 
                          src={request.from.profilePicture} 
                          alt={request.from.name} 
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{request.from.name}</p>
                      <p className="text-sm text-gray-500">{request.from.email}</p>
                    </div>
                  </div>
                  <div className="flex mt-2">
                    <Button 
                      onClick={() => handleRespondToRequest(request._id, true)}
                      className="mr-2"
                    >
                      Accept
                    </Button>
                    <Button 
                      onClick={() => handleRespondToRequest(request._id, false)}
                      variant="secondary"
                    >
                      Decline
                    </Button>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </>
      )}
    </div>
  );
}
