import AWS from 'aws-sdk';
import axios from "axios";
import { getDeviceId, getDeviceToken, getPlatform } from "../utils/device";

const API_BASE_URL_V1 = process.env.NEXT_PUBLIC_API_BASE_PRODUCTION;
const API_BASE_URL_V2 = process.env.NEXT_PUBLIC_API_BASE_PRODUCTION_V2;
const API_KEY = process.env.NEXT_PUBLIC_API_KEY;
const PLATFORM = getPlatform();
const LANGUAGE = process.env.NEXT_PUBLIC_LANGUAGE || "en";
const API_BASE_URL = "https://api.wishwellvillage.com";
//const API_BASE_URL = "http://localhost:3010";
const S3_BUCKET = "prod-ww-ca-central-1-s3-data";
const S3_REGION = "ca-central-1";
const S3_FOLDER = "files/";
const COGNITO_IDENTITY_POOL_ID = "ca-central-1:1eb68894-217e-4bb5-9462-5a389854b225";

/* HELPER FUNCTIONS */
const toBase64 = (file) => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = (error) => reject(error);
    });
};

/* AWS Cognito Setup */
AWS.config.update({
    region: S3_REGION,
    credentials: new AWS.CognitoIdentityCredentials({
        IdentityPoolId: COGNITO_IDENTITY_POOL_ID
    })
});

const s3 = new AWS.S3();

export const uploadMedia = async (file) => {
    await AWS.config.credentials.getPromise();
    const fileName = `${Date.now()}-${file.name}`;
    const fileKey = `${S3_FOLDER}${fileName}`;

    const params = {
        Bucket: S3_BUCKET,
        Key: fileKey,
        Body: file,
        ContentType: file.type,
        ACL: 'private'
    };

    try {
        const res = await s3.putObject(params).promise();
        console.log("S3 upload response:", res);
        console.log("✅ File uploaded successfully:", fileKey);
        return `https://${S3_BUCKET}.s3.${S3_REGION}.amazonaws.com/${fileKey}`;
    } catch (error) {
        console.error("S3 upload error:", error);
        throw new Error(`Failed to upload media: ${error.message}`);
    }
};

export const getSignedImageUrl = async (key) => {
    // Ensure AWS credentials are available and refreshed
    await AWS.config.credentials.getPromise();

    const params = {
        Bucket: S3_BUCKET,
        Key: key,
        Expires: 60, // URL valid for 60 seconds
    };

    const url = s3.getSignedUrl("getObject", params);
    console.log("🔗 Signed URL:", url);
    return url;
};

/* USER FUNCTIONS */
export const signupUser = async (data) => {
    const authString = `${process.env.NEXT_PUBLIC_AUTH_USERNAME}:${process.env.NEXT_PUBLIC_AUTH_PASSWORD}`;
    const authCredentials = Buffer.from(authString, "utf-8").toString("base64");

    try {
        const deviceId = getDeviceId();
        const deviceToken = await getDeviceToken();

        const response = await axios.post(
            `${API_BASE_URL}/api/v3/user/signup`,
            {
                email: data.email,
                password: data.password,
                deviceId,
                deviceToken,
            },
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Basic ${authCredentials}`,
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
            }
        );

        return response.data;
    } catch (error) {
        console.error("Signup API Error:", error.response ? error.response.data : error.message);
        throw error.response?.data || error.message;
    }
};

export const socialSignupUser = async ({ email, socialId, loginType, name, profilePicture }) => {
    const authString = `${process.env.NEXT_PUBLIC_AUTH_USERNAME}:${process.env.NEXT_PUBLIC_AUTH_PASSWORD}`;
    const authCredentials = Buffer.from(authString, "utf-8").toString("base64");

    try {
        const deviceId = getDeviceId();
        const deviceToken = await getDeviceToken();

        const response = await axios.post(
            `${API_BASE_URL}/api/v1/user/social-signup`,
            {
                email,
                socialId,
                loginType,
                name,
                profilePicture,
                deviceId,
                deviceToken,
            },
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Basic ${authCredentials}`,
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
            }
        );

        return response.data;
    } catch (error) {
        console.error("Social Signup Error:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export async function loginUser(credentials) {
    try {
        const authString = `${process.env.NEXT_PUBLIC_AUTH_USERNAME}:${process.env.NEXT_PUBLIC_AUTH_PASSWORD}`;
        const authCredentials = Buffer.from(authString, "utf-8").toString("base64");
        const deviceId = getDeviceId();
        const deviceToken = await getDeviceToken();

        const response = await fetch(`${API_BASE_URL}/api/v1/user/login`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Basic ${authCredentials}`,
                platform: PLATFORM,
                language: LANGUAGE,
            },
            body: JSON.stringify({
                email: credentials.email,
                password: credentials.password,
                deviceId,
                deviceToken,
            }),
        });

        if (!response.ok) throw new Error("Invalid credentials");

        const data = await response.json();

        localStorage.setItem("accessToken", data.accessToken);
        localStorage.setItem("userProfile", JSON.stringify(data.data));

        return data;
    } catch (error) {
        throw new Error(error.message || "Login failed");
    }
}

export const getProfile = async (userId, accessToken) => {
    try {
        const response = await axios.get(`${API_BASE_URL}/api/v1/user/profile`, {
            params: { userId },
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${accessToken}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
        });

        return response.data;
    } catch (error) {
        console.log("❌ Error fetching user profile:", error.response?.data || error.message);
        throw error;
    }
};

export const updateProfile = async (data, accessToken) => {
    try {
        const response = await fetch(`${API_BASE_URL}/api/v1/user/edit-profile`, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${accessToken}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
            body: JSON.stringify(data),
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Failed to update profile: ${response.status} - ${errorText}`);
        }

        return await response.json();
    } catch (error) {
        console.error("❌ Error updating profile:", error);
        throw error;
    }
};

export const updateProfilePicture = async (imageFile, accessToken) => {
    try {
        const uploadedImageUrl = await uploadMedia(imageFile);
        const response = await updateProfile({ profilePicture: uploadedImageUrl }, accessToken);
        return response;
    } catch (error) {
        console.error("❌ Error updating profile picture:", error);
        throw error;
    }
};

export const updateUserSettings = async (settings, accessToken) => {
    try {
        const response = await fetch(`${API_BASE_URL}/api/v1/user/setting`, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${accessToken}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
            body: JSON.stringify(settings),
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Failed to update settings: ${response.status} - ${errorText}`);
        }

        return await response.json();
    } catch (error) {
        console.error("❌ Error updating user settings:", error);
        throw error;
    }
};

export const changeUserPassword = async (currentPassword, newPassword, accessToken) => {
    try {
        const response = await fetch(`${API_BASE_URL}/api/v1/user/change-password`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${accessToken}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
            body: JSON.stringify({ currentPassword, newPassword }),
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Failed to change password: ${response.status} - ${errorText}`);
        }

        return await response.json();
    } catch (error) {
        console.error("❌ Error changing password:", error);
        throw error;
    }
};

/* COMMUNITY FUNCTIONS */
export const getCommunities = async (accessToken, params = {}) => {
    //console.log("API fetching communities with params:", params);
    try {
        const response = await axios.get(`${API_BASE_URL}/api/v2/community`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${accessToken}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
            params,
        });

        //console.log("Communities data:", response.data);

        return response.data;
    } catch (error) {
        console.error("❌ Error fetching communities:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const createCommunity = async (data, accessToken) => {
    try {
        const response = await axios.post(
            `${API_BASE_URL}/api/v2/community`,
            data,
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${accessToken}`,
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
            }
        );

        return response.data;
    } catch (error) {
        console.error("❌ Error creating community:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const updateCommunity = async (communityId, data, accessToken) => {
    try {
        const response = await axios.put(
            `${API_BASE_URL}/api/v2/community/edit-community`,
            {
                communityId, // backend requires this in the payload, not in the URL
                ...data,
            },
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${accessToken}`,
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
            }
        );

        return response.data;
    } catch (error) {
        console.error("❌ Error updating community:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const deleteCommunity = async (communityId, accessToken) => {
    try {
        const response = await axios.post(
            `${API_BASE_URL}/api/v1/community/delete-community`,
            { communityId },
            {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                    "Content-Type": "application/json",
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
            }
        );

        console.log("✅ Community deleted:", response.data);

        // Axios automatically throws on non-2xx, so we don't need to check `ok` or parse JSON manually
        return response.data;
    } catch (err) {
        console.error("❌ Error deleting community:", err);
        throw err;
    }
};

export const getCommunityDetail = async (communityId, accessToken) => {
    try {
        const response = await axios.get(
            `${API_BASE_URL}/api/v2/community/community-detail`,
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${accessToken}`,
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
                params: { communityId },
            }
        );

        console.log("✅ Community detail loaded:", response.data);
        return response.data;
    } catch (error) {
        console.error("❌ Error fetching community detail:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const getPublicCommunityDetail = async (communityId) => {
    try {
        const response = await axios.get(
            `${API_BASE_URL}/api/v2/community/public-community-detail`,
            {
                headers: {
                    "Content-Type": "application/json",
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
                params: { communityId },
            }
        );

        console.log("✅ Public community detail loaded:", response.data);
        return response.data;
    } catch (error) {
        console.error("❌ Error fetching public community detail:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const requestToJoinCommunity = async (communityId, accessToken) => {
    try {
        const response = await axios.post(
            `${API_BASE_URL}/api/v2/community/join-request`,
            { communityId },
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${accessToken}`,
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
            }
        );

        console.log("✅ Join request sent:", response.data);
        return response.data;
    } catch (error) {
        console.error("❌ Error sending join request:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const respondToJoinRequest = async (communityId, userId, accept, accessToken) => {
    try {
        const response = await axios.put(
            `${API_BASE_URL}/api/v2/community/join-request/respond`,
            { communityId, userId, accept },
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${accessToken}`,
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
            }
        );

        console.log("✅ Join request response sent:", response.data);
        return response.data;
    } catch (error) {
        console.error("❌ Error responding to join request:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const getCommunityJoinRequests = async (communityId, accessToken, pageNo = 1, limit = 20) => {
    try {
        const response = await axios.get(
            `${API_BASE_URL}/api/v2/community/join-requests`,
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${accessToken}`,
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
                params: { communityId, pageNo, limit },
            }
        );

        console.log("✅ Community join requests loaded:", response.data);
        return response.data;
    } catch (error) {
        console.error("❌ Error fetching community join requests:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const leaveCommunity = async (communityId, accessToken) => {
    try {
        const response = await axios.post(
            `${API_BASE_URL}/api/v1/community/leave-community`,
            { communityId },
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${accessToken}`,
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
            }
        );

        console.log("✅ Left community:", response.data);
        return response.data;
    } catch (error) {
        console.error("❌ Error leaving community:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};


/* WISHES FUNCTIONS */
export const getGlobalWishes = async (accessToken, {
    searchKey = "",
    pageNo = 1,
    limit = 20,
    disasterCategory,
} = {}) => {
    try {
        const deviceId = getDeviceId();
        const deviceToken = await getDeviceToken();
        //console.log("Device ID:", deviceId);
        //console.log("Device Token:", deviceToken);
        const params = {
            pageNo,
            limit,
            ...(searchKey && { searchKey }),
            ...(disasterCategory && { disasterCategory }),
        };

        const response = await axios.get(`${API_BASE_URL}/api/v1/common/global-wishes`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${accessToken}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
            params,
        });

        return response.data;
    } catch (error) {
        console.error("❌ Error fetching global wishes:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const getRequestedWishes = async (accessToken, userId, {
    searchKey = '',
    communityId = [],
    pageNo = 1,
    limit = 20,
} = {}) => {
    try {
        const params = {
            userId,
            ...(searchKey && { searchKey }),
            ...(communityId.length > 0 && { communityId: communityId.join(',') }),
            pageNo,
            limit,
        };

        const response = await axios.get(`${API_BASE_URL}/api/v1/wishes/requested-wishes`, {
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${accessToken}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
            params,
        });

        return response.data;
    } catch (error) {
        console.error('❌ Error fetching requested wishes:', error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const getReceivedWishes = async (accessToken, userId, {
    searchKey = '',
    communityId = [],
    pageNo = 1,
    limit = 20,
} = {}) => {
    try {
        const params = {
            userId,
            ...(searchKey && { searchKey }),
            ...(communityId.length > 0 && { communityId: communityId.join(',') }),
            pageNo,
            limit,
        };

        const response = await axios.get(`${API_BASE_URL}/api/v1/wishes/received-wishes`, {
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${accessToken}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
            params,
        });

        return response.data;
    } catch (error) {
        console.error('❌ Error fetching received wishes:', error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const createWish = async (accessToken, wishData) => {
    try {
        const response = await axios.post(
            `${API_BASE_URL}/api/v2/wishes`,
            wishData,
            {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`,
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
            }
        );

        return response.data;
    } catch (error) {
        console.error('❌ Error creating wish:', error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const deleteWish = async (accessToken, wishId, status = 'CLOSED') => {
    try {
        const response = await axios.post(
            `${API_BASE_URL}/api/v1/wishes/delete-wish`,
            { wishId, status },
            {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`,
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
            }
        );
        return response.data;
    } catch (error) {
        console.error("❌ Error deleting wish:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const editWish = async (accessToken, wishData) => {
    try {
        const response = await axios.put(
            `${API_BASE_URL}/api/v2/wishes/edit-wish`,
            wishData,
            {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`,
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
            }
        );

        return response.data;
    } catch (error) {
        console.error('❌ Error editing wish:', error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export async function getWishDetails(accessToken, wishId) {
    try {
        const res = await axios.get(`${API_BASE_URL}/api/v1/wishes/wish-detail?wishId=${wishId}`, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
        });


        return res.data;
    } catch (err) {
        console.error('❌ getWishDetails error:', err);
        throw err;
    }
}

export const getBlessingsByWishId = async (accessToken, wishId, pageNo = 1, limit = 20) => {
    try {
        const response = await axios.get(`${API_BASE_URL}/api/v1/blessing/wishes-bless`, {
            params: {
                wishId,
                pageNo,
                limit,
            },
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${accessToken}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
        });

        return response.data;
    } catch (error) {
        console.error("❌ Error fetching wish blessings:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const getPinnedWishes = async (accessToken) => {
    try {
        const response = await axios.get(`${API_BASE_URL}/api/v1/wishes/pinned-wishes`, {
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${accessToken}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
        });

        return response.data;
    } catch (error) {
        console.error("❌ Error fetching wish blessings:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const pinUnpinWish = async (accessToken, subjectId, pin = true) => {
    try {
        const type = pin ? "ADD" : "REMOVE";
        const response = await axios.post(
            `${API_BASE_URL}/api/v1/wishes/pinned-wishes`,
            { subjectId, type },
            {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`,
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
            }
        );
        return response.data;
    } catch (error) {
        console.log("❌ Error pinning/unpinning wish via API:", error.response?.data || error.message);
        if (error.response?.data.type === "LIMIT_EXCEEDED") {
            console.log("❌ Limit exceeded!");
            return error.response?.data;
        } else {
            throw error.response?.data || error.message;
        }
    }
};

/* BLESSING FUNCTIONS */
export async function getBlessingLibrary(accessToken, type = "MEDITATION", pageNo = 1, limit = 100) {
    //console.log("🔍 Fetching blessings with token:", accessToken);

    try {
        const response = await axios.get(
            `https://api.wishwellvillage.com/api/v1/common/blessing-library`,
            {
                params: { type, pageNo, limit },
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                    api_key: "ZUtTl7UnXU",
                    "Content-Type": "application/json",
                    platform: PLATFORM,
                    language: "en",
                },
            }
        );

        return response.data;
    } catch (error) {
        console.error("❌ Blessing library fetch failed:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

export const performBlessing = async (accessToken, data) => {
    console.log("🔄 Performing blessing with data:", data);
    try {
        const response = await axios.post(
            `${API_BASE_URL}/api/v1/blessing/guided-blessing`,
            data,
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${accessToken}`,
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
            }
        );

        console.log("✅ Blessing performed successfully:", response.data);

        return response.data;
    } catch (error) {
        console.error("❌ Error performing blessing:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};

/* GRATITUDE FUNCTIONS */
export const giveGratitude = async (accessToken, data) => {
    console.log("🔄 giving gratitude with data:", data);
    try {
        const response = await axios.post(
            `${API_BASE_URL}/api/v1/gratitude`,
            data,
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${accessToken}`,
                    api_key: API_KEY,
                    platform: PLATFORM,
                    language: LANGUAGE,
                },
            }
        );

        console.log("✅ Gratitude performed successfully:", response.data);

        return response.data;
    } catch (error) {
        console.error("❌ Error giving gratitude:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
};



export const reportGratitude = async (accessToken, gratitudeId, reason, description = "") => {
    console.log("🔄 Reporting gratitude:", gratitudeId);
    try {
        // Use the general reportContent function with type GRATITUDE
        const result = await reportContent({
            subjectId: gratitudeId,
            reportType: reason,
            description,
            type: 'GRATITUDE',
            token: accessToken
        });

        console.log("✅ Gratitude reported successfully:", result);
        return result;
    } catch (error) {
        console.error("❌ Error reporting gratitude:", error);
        throw error;
    }
};

export const reportBlessing = async (accessToken, blessingId, reason, description = "") => {
    console.log("🔄 Reporting blessing:", blessingId);
    try {
        // Use the general reportContent function with type BLESSING
        const result = await reportContent({
            subjectId: blessingId,
            reportType: reason,
            description,
            type: 'BLESSING',
            token: accessToken
        });

        console.log("✅ Blessing reported successfully:", result);
        return result;
    } catch (error) {
        console.error("❌ Error reporting blessing:", error);
        throw error;
    }
};

/* WELL FUNCTIONS */
export const getWellData = async (accessToken, pageNo = 1, limit = 100) => {
    console.log("🔍 Getting well data");

    try {
        const response = await axios.get(
            `https://api.wishwellvillage.com/api/v1/blessing/well`,
            {
                params: { pageNo, limit },
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                    api_key: "ZUtTl7UnXU",
                    "Content-Type": "application/json",
                    platform: PLATFORM,
                    language: "en",
                },
            }
        );

        return response.data;
    } catch (error) {
        console.error("❌ Blessing library fetch failed:", error.response?.data || error.message);
        throw error.response?.data || error.message;
    }
}

/* COMPASSION MAP FUNCTIONS */
export const getCompassionMapData = async (accessToken, type = 2) => {
    console.log("🔍 Fetching compassion map data, type:", type);
    try {
        const response = await axios.get(`${API_BASE_URL}/api/v1/wishes/wish-location`, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
            params: {
                type,
            },
        });

        console.log("✅ Compassion map data loaded:", response.data);

        return response.data;
    } catch (error) {
        console.error("❌ Error fetching compassion map data:", error.response?.data || error.message);
        throw error;
    }
};

/* REPORT FUNCTIONS */
export async function reportContent({ subjectId, reportType, description, type, token }) {
    const endpoint = type
        ? '/api/v1/report/report'
        : subjectId.includes('wish')
            ? '/api/v1/report/report-wish'
            : '/api/v1/report/report-community';

    const body = type
        ? { subjectId, reportType, description, type }
        : { subjectId, reportType, description };

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
            api_key: API_KEY,
            platform: PLATFORM,
            language: LANGUAGE,
        },
        body: JSON.stringify(body),
    });

    if (!response.ok) throw new Error('Failed to report content');
    return response.json();
}

/* FRIENDS FUNCTIONS */

export async function getFriends(token, skip = 0, limit = 20) {
    try {
        const response = await fetch(`${API_BASE_URL}/api/v1/friends?skip=${skip}&limit=${limit}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || "Failed to fetch friends");
        }

        return await response.json();
    } catch (error) {
        console.error("Error fetching friends:", error);
        throw error;
    }
}

export async function getFriendRequests(token, skip = 0, limit = 20) {
    try {
        const response = await fetch(`${API_BASE_URL}/api/v1/friend/requests?skip=${skip}&limit=${limit}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || "Failed to fetch friend requests");
        }

        return await response.json();
    } catch (error) {
        console.error("Error fetching friend requests:", error);
        throw error;
    }
}

export async function sendFriendRequest(userId, token) {
    try {
        const response = await fetch(`${API_BASE_URL}/api/v1/friend/request`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
            body: JSON.stringify({ userId }),
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || "Failed to send friend request");
        }

        return await response.json();
    } catch (error) {
        console.error("Error sending friend request:", error);
        throw error;
    }
}

export async function respondToFriendRequest(requestId, accept, token) {
    try {
        const response = await fetch(`${API_BASE_URL}/api/v1/friend/request/respond`, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
            body: JSON.stringify({ requestId, accept }),
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || "Failed to respond to friend request");
        }

        return await response.json();
    } catch (error) {
        console.error("Error responding to friend request:", error);
        throw error;
    }
}

export async function searchUsers(searchTerm, token) {
    try {
        const response = await fetch(`${API_BASE_URL}/api/v1/users/search?term=${encodeURIComponent(searchTerm)}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
                api_key: API_KEY,
                platform: PLATFORM,
                language: LANGUAGE,
            },
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || "Failed to search users");
        }

        return await response.json();
    } catch (error) {
        console.error("Error searching users:", error);
        throw error;
    }
}
