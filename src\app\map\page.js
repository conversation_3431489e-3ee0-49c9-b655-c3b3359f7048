// src/app/(tabs)/map-debug/page.js
"use client";


import { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup } from 'react-leaflet';
import MarkerClusterGroup from 'react-leaflet-cluster-4-next';
import { useAuth } from "@/context/UserProvider";
import { useCompassionMap } from "@/context/CompassionMapProvider";
import LoadingIndicator from "@/components/LoadingIndicator";
import { mapDebugPoints } from "@/constants/mapDebugPoints";
import UnderwaterBackground from "@/components/UnderwaterBackground";


import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import iconUrl from 'leaflet/dist/images/marker-icon.png';
import iconShadow from 'leaflet/dist/images/marker-shadow.png';
import { useRouter } from "next/navigation";

const filterOptions = [
    { label: "All Time", type: 0 },
    { label: "Last 12h", type: 1 },
    { label: "Last 24h", type: 2 },
    { label: "Last 7d", type: 3 },
    { label: "Last 30d", type: 4 },
];

export default function CompassionMapDebugPage() {
    // Fix Leaflet's default icon path
    delete L.Icon.Default.prototype._getIconUrl;
    L.Icon.Default.mergeOptions({
        iconUrl: "/img/marker-icon.png",
        shadowUrl: null,
    });
    const router = useRouter()
    const { accessToken } = useAuth();
    const [points, setPoints] = useState(mapDebugPoints)
    const [leaveAnimation, setLeaveAnimation] = useState(false)
    const hasFetched = useRef(false);
    const {
        mapData,
        loadingMapData,
        mapError,
        selectedFilter,
        fetchMapData,
    } = useCompassionMap();

    useEffect(() => {
        if (accessToken && !hasFetched.current) {
            console.log("Fetching compassion map data on mount...");
            fetchMapData(accessToken, selectedFilter);
            hasFetched.current = true;
        }
    }, [accessToken]);

    const handleLeave = async (callback) => {
        setLeaveAnimation(true)
        await sleep(520)
        callback()
    }

    return (
        <>
            <UnderwaterBackground />
            <div className={`md:py-17 h-full overflow-x-hidden overflow-y-auto opacity-0 animate-fade-in-blur ${leaveAnimation ? 'animate-slide-left-out overflow-hidden' : 'overflow-y-auto'}`}>
                <div className="max-w-full md:overflow-x-hidden md:mx-20 relative md:mt-10 shadow-md bg-transparent md:bg-beige md:rounded-2xl">

                    <div className="absolute top-0 left-0 px-4 md:px-6 w-full h-full pointer-events-none z-600">

                        <div className="flex gap-2 my-6 md:ml-4 md:bottom-0 md:left-0 md:absolute pointer-events-auto">
                            {filterOptions.map(({ label, type }) => (
                                <button
                                    key={type}
                                    onClick={() => accessToken && fetchMapData(accessToken, type)}
                                    className={`px-3 py-2 rounded-2xl cursor-pointer transition-all font-bold duration-200 text-sm ${selectedFilter === type
                                        ? 'bg-gradient-to-r from-darkorange to-orange text-white'
                                        : 'bg-gradient-to-r from-darkorange to-orange text-white opacity-70'
                                        }`}
                                >
                                    {label}
                                </button>
                            ))}
                        </div>
                        <div className="grid grid-cols-2 md:mt-4 md:grid-cols-4 gap-4 md:mx-2 md:gap-8 space-evenly">
                            <div className="relative h-[72px] rounded-2xl overflow-hidden">
                                <img src="/img/bg-rectangle-graphic-1.png" alt="Background Graphic" className="absolute top-0 left-0 w-full h-full object-cover" />
                                <div className="relative h-full grid grid-cols-[auto_auto] items-end justify-start text-white px-4 pb-1">
                                    <div className="pb-4 pr-1">
                                        <img src="/img/blessing.svg" width="24px" height="auto" alt="Blessing icon" />
                                    </div>
                                    <div className="pb-2">
                                        <p className="text-[20px] font-bold">1.1k</p>
                                        <p className="text-[12px] font-bold">Blessings</p>
                                    </div>
                                </div>
                            </div>
                            <div className="relative h-[72px] rounded-2xl overflow-hidden">
                                <img src="/img/bg-rectangle-graphic-2.png" alt="Background Graphic" className="absolute top-0 left-0 w-full h-full object-cover" />
                                <div className="relative h-full grid grid-cols-[auto_auto] items-end justify-start text-white px-4 pb-1">
                                    <div className="pb-4 pr-1">
                                        <img src="/img/wish.svg" width="24px" height="auto" alt="Blessing icon" />
                                    </div>
                                    <div className="pb-2">
                                        <p className="text-[20px] font-bold">989</p>
                                        <p className="text-[12px] font-bold">Active Wishes</p>
                                    </div>
                                </div>
                            </div>
                            <div className="relative h-[72px] rounded-2xl overflow-hidden">
                                <img src="/img/bg-rectangle-graphic-3.png" alt="Background Graphic" className="absolute top-0 left-0 w-full h-full object-cover" />
                                <div className="relative h-full grid grid-cols-[auto_auto] items-end justify-start text-white px-4 pb-1">
                                    <div className="pb-4 pr-1">
                                        <img src="/img/gratitude.svg" width="24px" height="auto" alt="Blessing icon" />
                                    </div>
                                    <div className="pb-2">
                                        <p className="text-[20px] font-bold">654</p>
                                        <p className="text-[12px] font-bold">Gratitude</p>
                                    </div>
                                </div>
                            </div>
                            <div className="relative h-[72px] rounded-2xl overflow-hidden">
                                <img src="/img/bg-rectangle-graphic-4.png" alt="Background Graphic" className="absolute top-0 left-0 w-full h-full object-cover" />
                                <div className="relative h-full grid items-end justify-start text-white px-4 pb-1">

                                    <div className="pb-2">
                                        <p className="text-[16px] font-bold">South Carolina</p>
                                        <p className="text-[12px] font-bold">Most Compassionate</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {loadingMapData && <p><LoadingIndicator /></p>}
                        {mapError && <p className="text-red-500">Error: {mapError.message}</p>}
                        <pre className="text-xs hidden bg-gray-100 text-black p-2 rounded overflow-auto max-h-[60vh]">
                            {JSON.stringify(mapData, null, 2)}
                        </pre>
                    </div>
                    <MapContainer center={[37.8, -96]} zoom={4}
                        zoomControl={false}
                        minZoom={3}
                        attributionControl={false}
                        className="h-dvh md:h-[70vh] w-full">
                        <TileLayer
                            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                            attribution="&copy; OpenStreetMap contributors"
                        />
                        <MarkerClusterGroup
                            chunkedLoading
                        >
                            {mapData?.data?.wishData?.map((wish, index) => (
                                <Marker
                                    key={index}
                                    position={[wish?.location?.coordinates?.[1], wish?.location?.coordinates?.[0]]}
                                    eventHandlers={{
                                        click: () => {
                                            router.push(`/wishes/${wish?._id}`)
                                        },
                                    }}
                                ></Marker>
                            ))}
                        </MarkerClusterGroup>
                    </MapContainer>

                </div>
            </div>
        </>
    );
}
