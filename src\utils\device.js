import { v4 as uuidv4 } from "uuid";

/**
 * Retrieves or generates a unique device ID.
 * @returns {string} deviceId
 */
export function getDeviceId() {
    if (typeof window === "undefined") return "server-device"; // SSR-safe fallback

    let deviceId = localStorage.getItem("deviceId");
    if (!deviceId) {
        deviceId = uuidv4();
        localStorage.setItem("deviceId", deviceId);
    }
    return deviceId;
}

/**
 * Returns a dummy device token for testing if Firebase is not configured.
 * @returns {Promise<string>} deviceToken
 */
export async function getDeviceToken() {
    return "dummy-device-token"; // 🚀 Always return a valid string to prevent Firebase errors
}

/**
 * Detects the platform type: Web, iOS, or Android.
 * @returns {number} 1 = iOS, 2 = Android, 3 = Web
 */
export function getPlatform() {
    if (typeof window === "undefined" || typeof navigator === "undefined") {
        return 3; // Default to Web for SSR
    }

    const userAgent = navigator.userAgent || navigator.vendor || window.opera;

    if (/android/i.test(userAgent)) {
        return 2; // Android
    }
    if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return 1; // iOS
    }
    return 3; // Web
}

// ✅ Ensure all functions are properly exported
export default { getDeviceId, getDeviceToken, getPlatform };
