'use client';

import React, { useState } from 'react';
import { useRouter } from "next/navigation";
import getImageUrl from '@/services/imageURL';
import { useCommunities } from '@/context/CommunityProvider';
import ReportModal from '@/components/ReportModal';

export default function CommunitiesPage() {
    const router = useRouter();
    const { communities, deleteCommunityById, loading, error } = useCommunities();
    const [reportModalOpen, setReportModalOpen] = useState(false);
    const [reportCommunityId, setReportCommunityId] = useState(null);

    if (loading) return <p className="p-4">Loading communities...</p>;
    if (error) return <p className="p-4 text-red-600">Error: {error.message || 'Failed to load'}</p>;

    return (
        <div className="p-4">
            <h1 className="text-xl font-bold mb-4">Communities</h1>

            <div className="mb-4">
                <h2 className="text-lg font-semibold mb-2">Joined Communities:</h2>

                <ul className="list-none">
                    {(communities?.data || []).map((community) => {
                        const { communityDetail } = community;
                        const imageUrl = getImageUrl(communityDetail?.image);

                        return (
                            <li key={community._id} className="mb-6">
                                <div className="p-4 border rounded shadow">
                                    {imageUrl && (
                                        <img
                                            src={imageUrl}
                                            alt={communityDetail?.name}
                                            className="w-10 h-10 rounded-full mb-2"
                                        />
                                    )}
                                    <h2 className="text-lg font-semibold">{communityDetail?.name}</h2>
                                    <p>{communityDetail?.purpose}</p>
                                    <p className="text-sm text-gray-500 mt-2">ID: {community._id}</p>
                                    <p className="text-sm text-gray-500">
                                        Created At: {new Date(communityDetail.createdAt).toLocaleString()}
                                    </p>
                                    <button
                                        onClick={() => router.push(`/communities/${community.communityDetail._id}/edit`)}
                                        className="mt-2 text-blue-500 underline text-sm"
                                    >
                                        Edit
                                    </button>
                                    <button
                                        onClick={() => {
                                            if (confirm("Are you sure you want to delete this community?")) {
                                                deleteCommunityById(community.communityDetail._id);
                                            }
                                        }}
                                        className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
                                    >
                                        Delete
                                    </button>
                                    <button
                                        onClick={() => {
                                            setReportCommunityId(community.communityDetail._id);
                                            setReportModalOpen(true);
                                        }}
                                        className="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 mt-2"
                                    >
                                        Report
                                    </button>
                                </div>
                            </li>
                        );
                    })}
                </ul>
            </div>

            <pre className="bg-gray-100 text-sm text-black p-4 rounded overflow-x-auto mb-6">
                {JSON.stringify(communities, null, 2)}
            </pre>

            <div className="flex gap-4">
                <button
                    onClick={() => router.push("/communities/create")}
                    className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                >
                    Create Community
                </button>
                <button
                    onClick={() => router.push("/dashboard")}
                    className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
                >
                    Go to Dashboard
                </button>
            </div>
            <ReportModal
                isOpen={reportModalOpen}
                onClose={() => setReportModalOpen(false)}
                type="COMMUNITY"
                subjectId={reportCommunityId}
            />
        </div>
    );
}
