'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useCommunities } from '@/context/CommunityProvider';
import { uploadMedia } from '@/services/api'; // adjust if needed

export default function CreateCommunityPage() {
    const { createCommunity } = useCommunities();
    const router = useRouter();

    const [name, setName] = useState('');
    const [purpose, setPurpose] = useState('');
    const [imageFile, setImageFile] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError(null);
        setLoading(true);

        try {
            let uploadedImageUrl = "";
            if (imageFile) {
                uploadedImageUrl = await uploadMedia(imageFile);
                console.log("📷 Uploaded image URL:", uploadedImageUrl);
            }

            await createCommunity({
                name,
                purpose,
                image: uploadedImageUrl,
                members: [
                    {
                        contactId: "000000000000000000000000",
                        isAppUser: false,
                        phoneNumber: "0000000000",
                        countryCode: "+1"
                    }
                ]
            });

            router.push('/communities');
        } catch (err) {
            setError(err?.message || "Something went wrong");
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="max-w-xl mx-auto p-6">
            <h1 className="text-2xl font-bold mb-4">Create a Community</h1>

            <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                    <label className="block font-medium mb-1">Name</label>
                    <input
                        type="text"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="w-full border border-gray-300 rounded px-3 py-2"
                        required
                    />
                </div>

                <div>
                    <label className="block font-medium mb-1">Purpose</label>
                    <textarea
                        value={purpose}
                        onChange={(e) => setPurpose(e.target.value)}
                        className="w-full border border-gray-300 rounded px-3 py-2"
                        rows={4}
                        required
                    />
                </div>

                <div>
                    <label className="block font-medium mb-1">Community Image (optional)</label>
                    <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => setImageFile(e.target.files[0])}
                        className="block w-full text-sm text-gray-700"
                    />
                </div>

                {error && <p className="text-red-600 text-sm">{error}</p>}

                <button
                    type="submit"
                    disabled={loading}
                    className="bg-blue-600 text-white font-semibold px-4 py-2 rounded hover:bg-blue-700"
                >
                    {loading ? 'Creating...' : 'Create Community'}
                </button>
            </form>
        </div>
    );
}