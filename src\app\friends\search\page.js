'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useFriends } from '@/context/FriendsProvider';
import { searchUsers } from '@/services/api';
import { useAuth } from '@/context/UserProvider';
import LoadingIndicator from '@/components/LoadingIndicator';
import Button from '@/components/Button';
import TextInput from '@/components/TextInput';
import BackButton from '@/components/BackButton';

export default function SearchFriendsPage() {
  const router = useRouter();
  const { accessToken, user } = useAuth();
  const { sendRequest, friends } = useFriends();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const timerId = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => {
      clearTimeout(timerId);
    };
  }, [searchTerm]);

  // Auto-search when debounced term changes
  useEffect(() => {
    if (debouncedSearchTerm) {
      handleSearch();
    }
  }, [debouncedSearchTerm]);

  const handleSearch = async () => {
    if (!debouncedSearchTerm.trim()) {
      setSearchResults([]);
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const results = await searchUsers(debouncedSearchTerm, accessToken);
      // Filter out current user from results
      const filteredResults = results.data.filter(u => u._id !== user?._id);
      setSearchResults(filteredResults || []);
    } catch (err) {
      console.error('❌ Error searching users:', err);
      setError(err.message || 'Failed to search users');
    } finally {
      setLoading(false);
    }
  };

  const handleSendRequest = async (userId) => {
    const success = await sendRequest(userId);
    if (success) {
      // Update UI to show request sent
      setSearchResults(prev => 
        prev.map(user => 
          user._id === userId 
            ? { ...user, requestSent: true } 
            : user
        )
      );
    }
  };

  const isFriend = (userId) => {
    return friends.some(friend => friend._id === userId);
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <BackButton />
        <h1 className="text-2xl font-bold ml-4">Find Friends</h1>
      </div>

      <div className="mb-6">
        <TextInput
          placeholder="Search by name or email"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full"
        />
      </div>

      {loading && <LoadingIndicator />}
      
      {error && <p className="text-red-500 mb-4">{error}</p>}

      {searchResults.length > 0 ? (
        <ul className="divide-y">
          {searchResults.map((user) => (
            <li key={user._id} className="py-4 flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-300 mr-3">
                  {user.profilePicture && (
                    <img 
                      src={user.profilePicture} 
                      alt={user.name} 
                      className="w-10 h-10 rounded-full object-cover"
                    />
                  )}
                </div>
                <div>
                  <p className="font-medium">{user.name}</p>
                  <p className="text-sm text-gray-500">{user.email}</p>
                </div>
              </div>
              
              {isFriend(user._id) ? (
                <span className="text-green-500">Friend</span>
              ) : user.requestSent ? (
                <span className="text-blue-500">Request Sent</span>
              ) : (
                <Button 
                  onClick={() => handleSendRequest(user._id)}
                  size="small"
                >
                  Add Friend
                </Button>
              )}
            </li>
          ))}
        </ul>
      ) : debouncedSearchTerm && !loading ? (
        <p className="text-gray-500">No users found matching &quot;{debouncedSearchTerm}&quot;</p>
      ) : null}
    </div>
  );
}
