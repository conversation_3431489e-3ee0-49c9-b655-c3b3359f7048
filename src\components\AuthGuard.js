'use client';

import { useAuth } from "../context/UserProvider";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import LoadingIndicator from "./LoadingIndicator";

export default function AuthGuard({ children }) {
    const { user, loading } = useAuth();
    const router = useRouter();

    useEffect(() => {
        if (!loading && !user) {
            router.push("/login");
        }
    }, [user, loading, router]);

    if (loading) {
        return <div className="min-h-screen grid justify-center items-center"><LoadingIndicator /></div>;
    }

    return <>{user ? children : null}</>;
}