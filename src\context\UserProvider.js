'use client';

import { createContext, useContext, useEffect, useState } from "react";
import { GoogleAuthProvider, signInWithPopup, OAuthProvider } from "firebase/auth";
import { auth } from "@/config/firebaseConfig";
import { useRouter } from "next/navigation";
import {
    getProfile,
    updateProfile,
    updateProfilePicture,
    updateUserSettings,
    changeUserPassword,
    socialSignupUser
} from "../services/api";


const AuthContext = createContext(null);

export function AuthProvider({ children }) {
    const [user, setUser] = useState(null);
    const [userProfile, setUserProfile] = useState(null);
    const [accessToken, setAccessToken] = useState(null);
    const [loading, setLoading] = useState(true);
    const router = useRouter();

    useEffect(() => {
        async function fetchUserData() {
            try {
                const storedUser = localStorage.getItem("user");
                const token = localStorage.getItem("accessToken");

                if (storedUser && token) {
                    const parsedUser = JSON.parse(storedUser);
                    setUser(parsedUser);
                    setAccessToken(token);
                    localStorage.setItem("user", JSON.stringify(parsedUser));
                    console.log("User data fetched from localStorage:", parsedUser);
                    const profileData = await getProfile(parsedUser._id, token);
                    if (profileData && profileData.error) {
                        console.error("Error fetching profile data:", profileData.error);
                        localStorage.removeItem("userProfile");
                        localStorage.removeItem("accessToken");
                        setUser(null);
                        setAccessToken(null);
                        return;
                    }
                    //console.log("Profile data fetched:", profileData);
                    if (profileData) {
                        setUserProfile(profileData);
                        localStorage.setItem("userProfile", JSON.stringify(profileData));
                    }
                }
            } catch (error) {
                console.log("Error parsing user data from localStorage:", error);
                localStorage.removeItem("userProfile");
                localStorage.removeItem("accessToken");
                setUser(null);
                setAccessToken(null);
                setUserProfile(null);
                router.push("/login");
            }
            setLoading(false);
        }
        fetchUserData();
    }, []);

    const login = async (userData, token) => {
        console.log("called login function");
        if (!userData || !token) {
            console.error("Missing user data or token during login:", userData, token);
            return;
        }

        localStorage.setItem("accessToken", token);
        setAccessToken(token);
        setUser(userData);
        localStorage.setItem("user", JSON.stringify(userData));
        const profileData = await getProfile(userData._id, token);
        if (profileData) {
            setUserProfile(profileData);
            localStorage.setItem("userProfile", JSON.stringify(profileData));
        };

        console.log("routing to dashboard");
        router.push("/dashboard");
    };

    const signInWithGoogle = async ({ login, onError }) => {
        const provider = new GoogleAuthProvider();

        try {
            const result = await signInWithPopup(auth, provider);
            const firebaseUser = result.user;

            const response = await socialSignupUser({
                email: firebaseUser.email,
                socialId: firebaseUser.uid,
                loginType: "google",
                name: firebaseUser.displayName,
                profilePicture: firebaseUser.photoURL,
            });

            //console.log('response:', response);

            if (response?.data?.accessToken) {
                login(response.data, response.data.accessToken);
            } else {
                throw new Error("No accessToken returned from socialSignupUser.");
            }
        } catch (error) {
            console.error("Google Sign-In Failed:", error);
            onError?.(error);
        }
    };

    const signInWithApple = async ({ login, onError }) => {
        const provider = new OAuthProvider("apple.com");

        try {
            const result = await signInWithPopup(auth, provider);
            const firebaseUser = result.user;

            const response = await socialSignupUser({
                email: firebaseUser.email,
                socialId: firebaseUser.uid,
                loginType: "apple",
                name: firebaseUser.displayName || "", // Apple may not provide name
                profilePicture: firebaseUser.photoURL || "", // Unlikely with Apple, but safe to include
            });

            console.log('response:', response);

            if (response?.data?.accessToken) {
                login(response.data, response.data.accessToken);
            } else {
                throw new Error("No accessToken returned from socialSignupUser.");
            }
        } catch (error) {
            console.error("Apple Sign-In Failed:", error);
            onError?.(error);
        }
    };

    const logout = () => {
        localStorage.removeItem("accessToken");
        localStorage.removeItem("user");
        localStorage.removeItem("userProfile");
        setUser(null);
        setAccessToken(null);
        setUserProfile(null);
        router.push("/login");
    };

    const updateUserProfile = async (updatedData) => {
        if (!user || !localStorage.getItem("accessToken")) return;

        try {
            const token = localStorage.getItem("accessToken");
            const updatedProfile = await updateProfile(updatedData, token);

            setUserProfile(updatedProfile);
            localStorage.setItem("userProfile", JSON.stringify(updatedProfile));

            console.log("✅ Profile updated successfully:", updatedProfile);
        } catch (error) {
            console.error("❌ Error updating profile:", error);
        }
    };

    const updateUserPicture = async (imageFile) => {
        if (!user || !localStorage.getItem("accessToken")) return;

        try {
            const token = localStorage.getItem("accessToken");
            const updatedUser = await updateProfilePicture(imageFile, token);

            setUser((prev) => ({ ...prev, profilePicture: updatedUser.profilePicture }));
            localStorage.setItem("user", JSON.stringify({ ...user, profilePicture: updatedUser.profilePicture }));

            console.log("✅ Profile picture updated successfully:", updatedUser);
        } catch (error) {
            console.error("❌ Error updating profile picture:", error);
        }
    };

    const updateSettings = async (settings) => {
        if (!user || !localStorage.getItem("accessToken")) return;

        try {
            const token = localStorage.getItem("accessToken");
            const updated = await updateUserSettings(settings, token);

            const newUserProfile = {
                ...userProfile,
                data: {
                    ...userProfile?.data,
                    ...settings
                }
            };
            setUserProfile(newUserProfile);
            localStorage.setItem("userProfile", JSON.stringify(newUserProfile));

            console.log("✅ Settings updated:", updated);
        } catch (error) {
            console.error("❌ Error updating settings:", error);
        }
    };

    const updatePassword = async (currentPassword, newPassword) => {
        if (!user || !localStorage.getItem("accessToken")) return;

        try {
            const token = localStorage.getItem("accessToken");
            const result = await changeUserPassword(currentPassword, newPassword, token);
            console.log("✅ Password changed:", result);
        } catch (error) {
            console.error("❌ Error changing password:", error);
        }
    };

    return (
        <AuthContext.Provider value={{
            user,
            userProfile,
            accessToken,
            login,
            signInWithGoogle,
            signInWithApple,
            logout,
            updateUserProfile,
            updateUserPicture,
            updateSettings,
            updatePassword,
            loading
        }}>
            {children}
        </AuthContext.Provider>
    );
}

export function useAuth() {
    return useContext(AuthContext);
}
