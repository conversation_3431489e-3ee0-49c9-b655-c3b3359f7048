'use client';

import { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useWishes } from '@/context/WishProvider';
import { useCommunities } from '@/context/CommunityProvider';
import { uploadMedia } from '@/services/api';
import getImageUrl from '@/services/imageURL';
import SelectInput from '@/components/SelectInput';
import TextAreaInput from '@/components/TextAreaInput';
import ImageInput from '@/components/ImageInput';
import Button from '@/components/Button';
import LoadingIndicator from '@/components/LoadingIndicator';
import BackButton from '@/components/BackButton';
import sleep from '@/utils/sleep';
import Asterix from '@/components/Asterix';
import Divider from '@/components/Divider';

const INTENTIONS = ['LOVE', 'PEACE', 'HEALTH', 'ABUNDANCE'];
const WISH_TYPES = [{ name: "Myself", value: "MYSE<PERSON>" }, { name: "Others", value: "OTHER" }];

export default function EditWishPage() {
    const { id: wishId } = useParams();
    const router = useRouter();


    const {
        requestedWishes,
        editWishById,
        refetchAllWishes,
        loading: loadingWishes,
    } = useWishes();

    const { communities } = useCommunities();

    const [form, setForm] = useState({
        description: '',
        intension: '',
        image: '',
        communityIds: [],
    });

    const [imageFile, setImageFile] = useState(null);
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const [leaveAnimation, setLeaveAnimation] = useState(false)

    // Load wish data
    useEffect(() => {
        if (!loadingWishes) {
            const found = requestedWishes?.data?.find((w) => w._id === wishId || w.wishDetail?._id === wishId);
            console.log('Found wish:', found, wishId, requestedWishes);
            if (found) {
                setForm((prev) => ({
                    ...prev,
                    description: found.wishDetail?.description || '',
                    intension: found.wishDetail?.intension || 'HEALTH',
                    image: found.wishDetail?.image || '',
                    communityIds: found.communityId || [],
                }));
            }
        }
    }, [wishId, requestedWishes, loadingWishes]);

    const handleChange = (value, name) => {
        setForm((prev) => ({ ...prev, [name]: value }));
    };

    const toggleCommunity = (id) => {
        setForm((prev) => ({
            ...prev,
            communityIds: prev.communityIds.includes(id)
                ? prev.communityIds.filter((cid) => cid !== id)
                : [...prev.communityIds, id],
        }));
    };

    const handleImageUpload = (file) => {
        if (file) {
            if (form.image.startsWith('blob:')) {
                URL.revokeObjectURL(form.image); // Clean up old preview
            }
            setImageFile(file);
            const previewUrl = URL.createObjectURL(file);
            setForm((prev) => ({ ...prev, image: previewUrl }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            let imageUrl = form.image;
            if (imageFile) {
                imageUrl = await uploadMedia(imageFile);
            }

            await editWishById({
                wishId,
                description: form.description,
                intension: form.intension,
                image: imageUrl,
                addCommunityId: form.communityIds,
            });

            await refetchAllWishes();
            router.push('/wishes');
        } catch (err) {
            setError(err.message || 'Something went wrong while updating the wish.');
        } finally {
            setLoading(false);
        }
    };

    const handleGoBack = async () => {
        setLeaveAnimation(true)
        await sleep(520)
        router.push('/wishes');
    }

    return (
        <div className={`md:py-17 relative h-full overflow-y-auto opacity-0 animate-slide-left-in ${leaveAnimation ? 'animate-slide-right-out' : ''}`}>
            <div className="p-6 relative max-w-3xl md:mt-10 mx-auto backdrop-blur-md bg-lightbeige shadow-md md:rounded-2xl">
                <BackButton
                    className='absolute top-13 left-6'
                    onClick={handleGoBack}
                />
                <h1 className="text-4xl mt-4 mb-8 font-heading text-center text-darkgreen">Edit</h1>
                <Divider />
                <form onSubmit={handleSubmit} className="space-y-4 mb-20">

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
                        <div className='grid gap-12'>
                            <div>
                                <p className='mb-2'>Please enter your desired wish<Asterix /></p>
                                <TextAreaInput
                                    rows="4"
                                    placeholder="Describe your wish..."
                                    value={form.description}
                                    onChange={(e) => handleChange(e.target.value, "description")}
                                    maxLength={250}
                                    required
                                />
                            </div>

                        </div>
                        <div className='grid gap-12'>
                            <div>
                                <p className='mb-2'>Select the intention for this wish<Asterix /></p>
                                <SelectInput options={INTENTIONS.map((type) => ({ value: type, name: type.toLowerCase() }))}
                                    handleSelect={(v) => handleChange(v, "intension")}
                                    value={form.intension}
                                />
                            </div>
                        </div>

                        <div>
                            <div>
                                <label className="block font-medium mb-1">Add an encouraging photo that relates to your wish<Asterix /></label>
                                <ImageInput current={getImageUrl(form.image)} handleChange={(file) => handleImageUpload(file)} />
                            </div>
                        </div>
                        <div>
                            <label className="block font-medium mb-1">Communities</label>
                            <div className={`grid gap-10 w-full rounded-2xl p-3 transition-all duration-200`}>
                                {(communities?.data || []).map((community) => {
                                    const { communityDetail } = community;
                                    return (
                                        <button
                                            type='button'
                                            key={communityDetail._id}
                                            onClick={() => { toggleCommunity(communityDetail._id) }}
                                            className={`px-4 py-2 rounded-2xl cursor-pointer transition-all duration-300 text-sm ${form.communityIds.includes(communityDetail._id)
                                                ? 'bg-gradient-to-r from-darkblue to-lightblue text-white font-bold'
                                                : 'bg-linear-65 from-beige to-orange/50 text-gray-700'
                                                }`}
                                        >
                                            {communityDetail.name}
                                        </button>
                                    )
                                })}
                            </div>
                        </div>
                    </div>
                    {error && <p className="my-6 text-red-500 mx-auto">{error}</p>}

                    <div className='grid gap-10 mt-12 mx-auto md:justify-center'>
                        <Button
                            type="submit"
                            stars={true}
                            disabled={(loading || !form.description || !form.image)}
                        >
                            {loading ? <LoadingIndicator /> : 'Update Wish'}
                        </Button>
                    </div>
                </form>
            </div>
        </div >
    );
}