'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useWishes } from '@/context/WishProvider';
import getImageUrl from '@/services/imageURL';

export default function PinnedWishesPage() {
    const router = useRouter();
    const { fetchPinnedWishes, pinnedWishes } = useWishes();

    useEffect(() => {
        fetchPinnedWishes();
    }, [fetchPinnedWishes]);

    if (!pinnedWishes || pinnedWishes.length === 0) {
        return <div className="p-4">No pinned wishes found.</div>
    }

    return (
        <div className="p-4">
            <h1 className="text-2xl font-bold mb-2">Pinned Wishes</h1>
            {pinnedWishes.map((wish) => (
                <div key={wish.wishDetail._id} className="mb-4">
                    <p className="text-gray-600 mb-2">{wish.wishDetail.description}</p>
                    {wish.wishDetail.image && (
                        <img
                            src={getImageUrl(wish.wishDetail.image)}
                            alt="Wish"
                            className="w-full rounded-xl mb-4 object-cover max-h-[400px]"
                        />
                    )}
                    <button
                        className="px-4 py-2 bg-blue-600 text-white rounded-full mb-4"
                        onClick={() => router.push(`/wishes/${wish.wishDetail._id}`)}
                    >
                        View Details
                    </button>
                </div>
            ))}
            <pre>
                <code className="text-sm text-white-700 whitespace-pre-wrap">
                    {JSON.stringify(pinnedWishes, null, 2)}
                </code>
            </pre>
        </div>
    );
}