'use client';

import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import Image from "next/image";
import { useAuth } from "@/context/UserProvider";

export default function Home() {
	const router = useRouter();
	const pathname = usePathname();
	const { user } = useAuth();

	useEffect(() => {
		if (user) {
			router.push("/dashboard");
		} else if (pathname !== "/signup") {
			router.push("/login");
		}
	}, [user, router, pathname]);

	return (
		<div></div>
	);
}
