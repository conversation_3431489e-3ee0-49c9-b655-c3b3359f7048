export default function TextInput({ className = "", placeholder = "", children, ...props }) {
    return (<div className="relative">
        <input
            {...props}
            className={`relative w-full py-3 px-4 border rounded-2xl border-lightblue focus-visible:bg-fadedblue focus-visible:outline-darkgreen/70 outline-lightblue transition-colors duration-200 ${className}`}
        />
        <div className={`absolute left-4 focus-visible:top-[2px] focus-visible:left-5 ${props.value ? 'top-[2px] text-[10px] text-gray-500' : 'top-3 text-gray-700'} transition-all duration-200 pointer-events-none`}>{placeholder}</div>
    </div>)
}