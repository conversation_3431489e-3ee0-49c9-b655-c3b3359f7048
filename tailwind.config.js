/** @type {import('tailwindcss').Config} */
module.exports = {
    content: ["./src/**/*.{js,jsx,ts,tsx}"],
    theme: {
        extend: {
            fontFamily: {
                'heading': ['Calistoga', 'serif'],
                'subheading': ['Aileron', 'sans-serif'],
                'body': ['Aileron', 'sans-serif'],
                'sans': ['Aileron', 'sans-serif'],
            },
            colors: {
                'beige': '#faf5e8',
                'lightblue': '#6a8798',
                'fadedblue': '#ecf4f4',
                'lightgreen': '#cfdfde',
                'darkblue': '#1a2d39',
                'darkgreen': '#0e5d5c',
                'orange': '#e5b07c',
                'darkorange': '#d6a087',
                'lightbeige': '#fbf8f4',
                'error': '#c20114',
            },
            animation: {
                'fadein': 'fadeIn 0.6s cubic-bezier(0.26, 0.53, 0.74, 1.48) forwards',
                'fadeOut': 'fadeOut 0.5s cubic-bezier(0.26, 0.53, 0.74, 1.48) forwards',
                'slide-left-out': 'slide-left-out 0.5s ease-in forwards',
                'slide-left-in': 'slide-left-in 0.5s ease-out forwards',
                'slide-right-out': 'slide-right-out 0.5s ease-in forwards',
                'slide-right-in': 'slide-right-in 0.5s ease-out forwards',
                'fade-in-blur': 'fade-in-blur 0.5s ease-out forwards',
                'fade-out-blur': 'fade-out-blur 0.5s ease-in forwards',
                'grow': 'grow 0.5s ease forwards',
            },
        },
    },
    plugins: [],
};