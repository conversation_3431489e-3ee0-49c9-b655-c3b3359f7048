import Button from "./Button";
import { useEffect, useState } from "react";
export default function ImageInput({ handleChange, current }) {
    const [error, setError] = useState("");
    const [fileName, setFileName] = useState("");
    const [image, setImage] = useState(current || null);

    useEffect(() => {
        setImage(current)
    }, [current])

    const handleFileChange = (event) => {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.type.startsWith("image/")) {
            setError("Invalid file format. Please upload an image.");
            return;
        }

        setError("");
        setFileName(file.name);
        handleChange(file);

        // Handle image preview and set the image
        const reader = new FileReader();
        reader.onload = () => {
            setImage(reader.result);
        };
        reader.readAsDataURL(file);
    };

    return (
        <div className="relative">
            {!image && (<label className={`absolute top-15 left-0 w-full cursor-pointer select-none`}>
                <p className="text-sm mx-auto mt-6 max-w-fit bg-linear-65 from-darkblue to-lightblue py-2 px-4 text-white font-semibold rounded-2xl transition-all duration-200 hover:scale-105 active:scale-115">
                    Choose an image
                </p>
                <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                />
            </label>)}
            <div className="rounded-full mx-auto w-50 h-50 bg-beige">
                {image && <img src={image} className="w-50 h-50 animate-grow object-cover mx-auto rounded-full" alt="Image preview" />}
            </div>
            {image && <Button type="button" onClick={() => { setImage(null); handleChange(null) }} className="cursor-pointer absolute bottom-0 right-15" size="small">Clear</Button>}
            {error && <p className="text-red-500 text-sm">{error}</p>}
        </div>
    );
}