export default async function getUserLocation() {
    return new Promise((resolve, reject) => {
        if (!navigator.geolocation) {
            return reject(new Error("Geolocation is not supported by your browser."));
        }

        navigator.geolocation.getCurrentPosition(
            async (position) => {
                const { latitude, longitude } = position.coords;

                try {
                    const res = await fetch(
                        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${process.env.NEXT_PUBLIC_GOOGLE_API_KEY}`
                    );

                    const data = await res.json();

                    if (data.status !== "OK") {
                        return reject(new Error("Failed to reverse geocode location."));
                    }

                    const address = data.results[0]?.formatted_address || "";
                    const components = data.results[0]?.address_components || [];

                    const city =
                        components.find((c) => c.types.includes("locality"))?.long_name || "";
                    const state =
                        components.find((c) => c.types.includes("administrative_area_level_1"))?.long_name || "";
                    const country =
                        components.find((c) => c.types.includes("country"))?.long_name || "";

                    resolve({
                        address,
                        coordinates: [longitude, latitude],
                        city,
                        country,
                        state,
                    });
                } catch (err) {
                    reject(err);
                }
            },
            (error) => {
                reject(error);
            },
            { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
        );
    });
}
