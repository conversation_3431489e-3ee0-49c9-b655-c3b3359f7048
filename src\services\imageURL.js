export default function getImageUrl(rawUrl) {
    const baseCDN = process.env.NEXT_PUBLIC_CLOUD_FRONT_URL;
    if (!rawUrl) return null;

    if (rawUrl.includes("amazonaws.com/")) {
        const key = rawUrl.split("amazonaws.com/")[1]; // e.g., "files/DB24A753-0904-4DB0-98EE-40665C5FA55200.png"
        const filename = key.split("/").pop() || "";
        const baseName = filename.replace(/\.[^.]+$/, ""); // remove extension
        const extension = filename.split(".").pop()?.toLowerCase();

        const isIOSStyleUUID =
            /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}00$/i.test(baseName) ||
            /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(baseName); // add extra safety

        if (isIOSStyleUUID && ["png", "jpg", "jpeg", "webp"].includes(extension || "")) {
            return `${baseCDN}${filename}`; // strip "files/"
        }

        return `${baseCDN}${key}`; // default PWA upload
    }

    // Handle short-form keys (e.g., "files/abc.png")
    if (!rawUrl.startsWith("http")) {
        return `${baseCDN}${rawUrl}`;
    }

    return rawUrl;
}
