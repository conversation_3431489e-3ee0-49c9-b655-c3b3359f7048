'use client';
import { useState } from 'react';
import {
    reportWish,
    reportCommunity,
    reportBlessing,
    reportGratitude,
} from '@/context/ReportingController';
import { REPORT_REASONS } from '@/constants/report';

export default function ReportModal({ isOpen, onClose, type, subjectId }) {
    const [reason, setReason] = useState('');
    const [description, setDescription] = useState('');
    const [error, setError] = useState('');
    const [success, setSuccess] = useState(false);
    const [loading, setLoading] = useState(false);

    if (!isOpen) return null;

    const handleSubmit = async () => {
        if (!reason || !description) {
            setError('Please select a reason and enter a description.');
            return;
        }

        setLoading(true);
        setError('');

        try {
            const payload = { reason, description };
            switch (type) {
                case 'WISH':
                    await reportWish({ wishId: subjectId, ...payload });
                    break;
                case 'COMMUNITY':
                    await reportCommunity({ communityId: subjectId, ...payload });
                    break;
                case 'BLESSING':
                    await reportBlessing({ blessingId: subjectId, ...payload });
                    break;
                case 'GRATITUDE':
                    await reportGratitude({ gratitudeId: subjectId, ...payload });
                    break;
                default:
                    throw new Error('Invalid report type');
            }

            setSuccess(true);
            setTimeout(() => {
                setSuccess(false);
                setReason('');
                setDescription('');
                onClose();
            }, 1500);
        } catch (err) {
            setError('Failed to submit report. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="report-modal-backdrop">
            <div className="report-modal-container">
                <h2>Report this {type.toLowerCase()}</h2>

                {success ? (
                    <p>Report submitted. Thank you!</p>
                ) : (
                    <>
                        <div>
                            <label>Reason</label>
                            <select value={reason} onChange={(e) => setReason(e.target.value)}>
                                <option value="">Select a reason</option>
                                {REPORT_REASONS.map((r) => (
                                    <option key={r.value} value={r.value}>
                                        {r.label}
                                    </option>
                                ))}
                            </select>
                        </div>

                        <div>
                            <label>Description</label>
                            <textarea
                                rows={3}
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                            />
                        </div>

                        {error && <p>{error}</p>}

                        <div>
                            <button onClick={onClose} disabled={loading}>
                                Cancel
                            </button>
                            <button onClick={handleSubmit} disabled={loading}>
                                {loading ? 'Submitting...' : 'Submit'}
                            </button>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
}
