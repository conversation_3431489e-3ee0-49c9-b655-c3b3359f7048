"use client";

import { useState } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useBlessings } from "@/context/BlessingsProvider";
import BlessingDetailModal from "@/components/BlessingDetailModal";

export default function BlessingsPage() {
    const router = useRouter();
    const { meditationBlessings, prayerBlessings, loadingBlessings } = useBlessings();
    const { id: wishId } = useParams();
    const [tab, setTab] = useState("MEDITATION");
    const [selectedBlessing, setSelectedBlessing] = useState(null);

    const blessings = tab === "MEDITATION" ? meditationBlessings : prayerBlessings;

    return (
        <div className="p-4">
            <h1 className="text-2xl font-bold mb-4">Choose a Blessing</h1>

            <div className="flex space-x-4 mb-6">
                {["MEDITATION", "PRAYER"].map((type) => (
                    <button
                        key={type}
                        className={`px-4 py-2 rounded-full font-semibold border transition-colors duration-200 ${
                            tab === type
                                ? "bg-blue-600 text-white border-blue-600"
                                : "bg-white text-blue-600 border-blue-600"
                        }`}
                        onClick={() => setTab(type)}
                    >
                        {type.charAt(0) + type.slice(1).toLowerCase()}
                    </button>
                ))}
            </div>

            {loadingBlessings ? (
                <p>Loading blessings...</p>
            ) : blessings.length === 0 ? (
                <p>No {tab.toLowerCase()} blessings available.</p>
            ) : (
                <div className="space-y-4">
                    {blessings.map((blessing) => (
                        <div
                            key={blessing._id}
                            onClick={() => setSelectedBlessing(blessing)}
                            className="p-4 border rounded-xl shadow-md bg-white hover:bg-gray-100 cursor-pointer transition"
                        >
                            <h2 className="text-lg font-semibold">{blessing.name}</h2>
                            <p className="text-sm text-gray-600">
                                By {blessing.authorName} — {blessing.voiceover} voice — {blessing.intension}
                            </p>
                        </div>
                    ))}
                </div>
            )}
            <button
                className="mt-6 px-4 py-2 bg-blue-600 text-white rounded-full"
                onClick={() => router.back()}
            >
                Back to Wish
            </button>

            {selectedBlessing && (
                <BlessingDetailModal
                    isOpen={true}
                    onClose={() => setSelectedBlessing(null)}
                    blessing={selectedBlessing}
                    wishId={wishId}
                />
            )}
        </div>
    );
}
