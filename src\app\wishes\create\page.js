'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useWishes } from '@/context/WishProvider';
import { useCommunities } from '@/context/CommunityProvider';
import { uploadMedia } from '@/services/api';
import SelectInput from '@/components/SelectInput';
import TextAreaInput from '@/components/TextAreaInput';
import TextInput from "@/components/TextInput";
import ImageInput from '@/components/ImageInput';
import Button from '@/components/Button';
import LoadingIndicator from '@/components/LoadingIndicator';
import BackButton from '@/components/BackButton';
import sleep from '@/utils/sleep';
import Asterix from '@/components/Asterix';
import Divider from '@/components/Divider';
import UnderwaterBackground from '@/components/UnderwaterBackground';

const INTENTIONS = ['LOVE', 'PEACE', 'HEALTH', 'ABUNDANCE'];
const WISH_TYPES = [{ name: "Myself", value: "MYSELF" }, { name: "Others", value: "OTHER" }];

export default function CreateWishPage() {
    const router = useRouter();
    const { createWish } = useWishes();
    const { communities } = useCommunities();

    const [title, setTitle] = useState('');
    const [description, setDescription] = useState('');
    const [intention, setIntention] = useState(INTENTIONS[0]);
    const [wishType, setWishType] = useState(WISH_TYPES[0].value);
    const [isGlobalWish, setIsGlobalWish] = useState(false);
    const [selectedCommunities, setSelectedCommunities] = useState([]);
    const [imageFile, setImageFile] = useState(null);

    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [leaveAnimation, setLeaveAnimation] = useState(false)
    const [geolocationStatus, setGeolocationStatus] = useState("prompt")



    const toggleCommunity = (id) => {
        setSelectedCommunities((prev) =>
            prev.includes(id) ? prev.filter((c) => c !== id) : [...prev, id]
        );
    };
    const handleGoBack = async () => {
        setLeaveAnimation(true)
        await sleep(520)
        router.push('/wishes');
    }
    useEffect(() => {
        navigator.permissions?.query({ name: "geolocation" }).then(status => {
            setGeolocationStatus(status.state)
        });
    }, [])

    const requestGeolocation = () => {
        navigator.geolocation.getCurrentPosition(
            () => { setGeolocationStatus("granted"); console.log("ho") },
            () => setGeolocationStatus("denied")
        );
    }

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError(null);

        try {
            let uploadedImageUrl = '';
            if (imageFile) {
                uploadedImageUrl = await uploadMedia(imageFile);
                console.log('📷 Uploaded image URL:', uploadedImageUrl);
            }

            await createWish({
                title,
                description,
                wishType,
                intension: intention,
                isGlobalWish,
                image: uploadedImageUrl || undefined,
                communityId: selectedCommunities,
            });
            await handleGoBack()
        } catch (err) {
            setError(err?.message || 'Something went wrong.');
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <UnderwaterBackground />

            <div className={`md:py-17 h-full overflow-y-auto animate-slide-left-in relative ${leaveAnimation ? 'animate-slide-right-out' : ''}`}>
                <div className="p-6 relative max-w-3xl md:mt-10 mx-auto bg-transparent shadow-md md:rounded-2xl">
                    <BackButton
                        className='absolute top-13 left-6'
                        onClick={handleGoBack}
                    />
                    <h1 className="text-4xl mt-4 mb-8 font-heading text-center text-darkgreen">Create a Wish</h1>
                    <Divider />
                    <form onSubmit={handleSubmit} className="space-y-4 mb-20">

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
                            <div>
                                <p className="mb-2">Wish Title</p>
                                <TextInput
                                    rows="1"
                                    placeholder="Enter a title for your wish..."
                                    value={title}
                                    onChange={(e) => setTitle(e.target.value)}
                                    className="border-[#e5b07c]! mb-5"
                                />
                                <p className='mb-2'>Please enter your desired wish<Asterix /></p>
                                <TextAreaInput
                                    rows="4"
                                    placeholder="Describe your wish..."
                                    value={description}
                                    onChange={(e) => setDescription(e.target.value)}
                                    required
                                />
                            </div>
                            <div className='grid gap-0'>
                                <div>
                                    <p className='mb-2'>Select the intention for this wish<Asterix /></p>
                                    <SelectInput options={INTENTIONS.map((type) => ({ value: type, name: type.toLowerCase() }))}
                                        handleSelect={(v) => setIntention(v)}
                                        value={intention}
                                        className="mb-5"
                                    />
                                    <p className='mb-2'>Who is this wish for?<Asterix /></p>
                                    <SelectInput options={WISH_TYPES.map(({ name, value }) => ({ value, name: name.toLowerCase() }))}
                                        handleSelect={(v) => setWishType(v)}
                                        value={wishType}
                                    />
                                </div>
                            </div>

                            <div>
                                <div>
                                    <label className="block font-medium mb-1">Add an encouraging photo that relates to your wish<Asterix /></label>
                                    <ImageInput handleChange={(file) => setImageFile(file)} />
                                </div>
                            </div>
                            <div>
                                <label className="block font-medium mb-1">Select one or more community<Asterix /></label>
                                <div className={`grid gap-10 w-full rounded-2xl p-3 transition-all duration-200`}>
                                    {(communities?.data || []).map((community) => {
                                        const { communityDetail } = community;
                                        return (
                                            <button
                                                type='button'
                                                key={communityDetail._id}
                                                onClick={() => { toggleCommunity(communityDetail._id) }}
                                                className={`px-4 py-2 rounded-2xl cursor-pointer transition-all duration-300 text-sm ${selectedCommunities.includes(communityDetail._id)
                                                    ? 'bg-gradient-to-r from-darkblue to-lightblue text-white font-bold'
                                                    : 'bg-linear-65 from-beige to-orange/50 text-gray-700'
                                                    }`}
                                            >
                                                {communityDetail.name}
                                            </button>
                                        )
                                    })}
                                </div>
                            </div>
                        </div>
                        {error && <p className="my-6 text-red-500 mx-auto">{error}</p>}
                        {geolocationStatus !== "granted" && (
                            <div className='mx-auto text-center'>
                                {geolocationStatus === "prompt" && <p className="mb-2 text-yellow-600 text-center">Oops! You must turn on location settings on to create a wish</p>}
                                {geolocationStatus === "denied" && <p className="mb-2 text-red-500 text-center">Geolocation permission denied. You must enable it to create a wish.</p>}
                                <Button size="small" type="button" onClick={requestGeolocation}>Enable Geolocation</Button>
                            </div>
                        )}
                        <div className='grid gap-10 mt-12 mx-auto md:justify-center'>
                            <Button
                                type="submit"
                                stars={true}
                                disabled={(loading || !description || !imageFile || !geolocationStatus === "granted")}
                            >
                                {loading ? <LoadingIndicator /> : 'Create Wish'}
                            </Button>
                        </div>
                    </form>
                </div>
            </div >
        </>
    );
}
