'use client';

import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { formatDistanceToNow } from 'date-fns';
import { useWishes } from '@/context/WishProvider';
import { useCommunities } from '@/context/CommunityProvider';
import ReportModal from '@/components/ReportModal';
import { useOutsideClick } from "@/utils/useOuterClick";
import getImageUrl from '@/services/imageURL';
import LoadingIndicator from '@/components/LoadingIndicator';
import RoundedButton from '@/components/RoundedButton';
import DotMenu from '@/components/DotMenu';
import sleep from '@/utils/sleep';
import Divider from '@/components/Divider';
import Button from '@/components/Button';
import DeleteModal from '@/components/DeleteModal';
import UnderwaterBackground from '@/components/UnderwaterBackground';


const TABS = ['Requested', 'Received', 'Global'];

export default function WishListPage() {
    const router = useRouter();
    const {
        requestedWishes,
        receivedWishes,
        globalWishes,
        loading,
        error,
        filters,
        setFilters,
        refetchAllWishes,
        deleteWishById,
        pinUnpinWishById
    } = useWishes();

    const { communities } = useCommunities();

    const [searchInput, setSearchInput] = useState(filters.searchKey || '');
    const [showCommunitiesList, setShowCommunitiesList] = useState(false)
    const communitiesList = useRef()
    const [selectedCommunities, setSelectedCommunities] = useState(filters.communityId || []);
    useOutsideClick(communitiesList.current, () => {
        if (showCommunitiesList) setShowCommunitiesList(false)
    }, [showCommunitiesList]);
    const [activeTab, setActiveTab] = useState('Requested');
    const [leaveAnimation, setLeaveAnimation] = useState(false)
    const [wishToDelete, setWishToDelete] = useState(null)

    const [reportModalOpen, setReportModalOpen] = useState(false);
    const [wishToReport, setWishToReport] = useState(null);

    useEffect(() => {
        const timeout = setTimeout(() => {
            setFilters((prev) => ({
                ...prev,
                searchKey: searchInput,
                communityId: selectedCommunities,
                pageNo: 1,
            }));
        }, 300);
        return () => clearTimeout(timeout);
    }, [searchInput, selectedCommunities]);

    const toggleCommunity = (id) => {
        setSelectedCommunities((prev) =>
            prev.includes(id) ? prev.filter((c) => c !== id) : [...prev, id]
        );
    };

    const handlePinUnpinWish = (wishId, pin) => {
        pinUnpinWishById(wishId, pin);
    }

    const wishes =
        activeTab === 'Requested'
            ? requestedWishes
            : activeTab === 'Received'
                ? receivedWishes
                : globalWishes;

    return (
        <>
            <UnderwaterBackground />

            <div className={`md:py-17 h-full overflow-x-hidden opacity-0 animate-fade-in-blur ${leaveAnimation ? 'animate-slide-left-out overflow-hidden' : 'overflow-y-auto'}`}>
                <div className="p-6 max-w-3xl relative pb-20 md:pb-auto min-h-full md:min-h-auto md:mt-10 mx-auto backdrop-blur-md shadow-md bg-transparent md:bg-lightbeige md:rounded-2xl">
                    <h1 className="text-5xl mt-4 mb-8 font-heading text-center text-darkgreen">Wishes</h1>
                    <RoundedButton onClick={async () => {
                        setLeaveAnimation(true);
                        await sleep(520);
                        router.push('/wishes/create')
                    }}
                        className='absolute block z-10 top-23 right-6'>+</RoundedButton>

                    <Divider className='my-9 md:my-6' />
                    {/* Tabs */}
                    <div className="flex gap-4 my-6">
                        {TABS.map((tab) => (
                            <button
                                key={tab}
                                onClick={() => setActiveTab(tab)}
                                className={`px-4 py-2 rounded-2xl cursor-pointer transition-all duration-200 text-sm ${activeTab === tab
                                    ? 'bg-gradient-to-r from-darkorange to-orange text-white'
                                    : 'bg-gradient-to-r from-darkorange/75 to-orange text-white opacity-70'
                                    }`}
                            >
                                {tab}
                            </button>
                        ))}
                    </div>

                    {/* Filters */}
                    <div className="my-6 grid md:grid-cols-2 gap-4 items-start">
                        {/* Search */}
                        <input
                            type="text"
                            placeholder="Search wishes..."
                            className="relative w-full py-2 px-4 border rounded-2xl border-lightblue focus-visible:bg-fadedblue focus-visible:outline-darkgreen/70 outline-lightblue transition-colors duration-200"
                            value={searchInput}
                            onChange={(e) => setSearchInput(e.target.value)}
                        />

                        {/* Community Multi-select */}
                        <div className='grid justify-end relative'>
                            <p className="font-semibold text-darkgreen text-right grid grid-cols-[auto_auto] gap-2 items-center cursor-pointer justify-end"
                                onClick={() => setShowCommunitiesList(!showCommunitiesList)}>Filter by Communities
                                <img src={showCommunitiesList ? '/img/caret-up.svg' : '/img/search-sort.svg'} width="32px" className={`align-middle ${showCommunitiesList ? 'w-[25px] ml-[7px]' : 'w-[32px]'} h-[32px] object-contain`} alt='Search icon.' />
                            </p>
                            <div ref={communitiesList} className={`grid gap-10 max-w-80 absolute z-10 right-2 top-10 bg-white rounded-2xl p-3 shadow-lg transition-all duration-200 ${showCommunitiesList ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}>
                                {(communities?.data || []).map((community) => {
                                    const { communityDetail } = community;
                                    return (
                                        <button
                                            key={communityDetail._id}
                                            onClick={() => { setShowCommunitiesList(false); toggleCommunity(communityDetail._id); }}
                                            className={`px-4 py-2 rounded-2xl cursor-pointer transition-all duration-300 text-sm ${selectedCommunities.includes(communityDetail._id)
                                                ? 'bg-gradient-to-r from-darkorange to-orange text-white'
                                                : 'bg-linear-65 from-beige to-orange/50 text-gray-700'
                                                }`}
                                        >
                                            {communityDetail.name}
                                        </button>
                                    )
                                })}
                            </div>
                        </div>
                    </div>

                    {/* List / States */}
                    {loading && <p className="text-gray-500"><LoadingIndicator /></p>}

                    {error && (
                        <div className="text-red-500 my-4 text-center">
                            <p className='pb-2'>Something went wrong: {error}</p>
                            <Button
                                onClick={refetchAllWishes}
                                size="small"
                            >
                                Retry
                            </Button>
                        </div>
                    )}

                    {!loading && wishes?.data?.length === 0 && (
                        <p className="text-gray-500">No wishes found in this tab.</p>
                    )}

                    <ul className="grid md:grid-cols-2 items-start gap-4">
                        {(wishes?.data || []).map((wish) => {
                            const imageUrl = getImageUrl(wish.wishDetail?.image);
                            const creationDate = formatDistanceToNow(new Date(wish.wishDetail.created), { addSuffix: true });
                            return (
                                <li
                                    key={wish._id}
                                    className="pt-6 rounded-2xl shadow-sm bg-white relative animate-fade-in-blur"
                                >
                                    <div className='grid grid-cols-[72px_auto] gap-5 px-6 pb-3 cursor-pointer'
                                        onClick={() => router.push(`/wishes/${wish._id}`)}
                                    >

                                        <img
                                            src={imageUrl}
                                            alt={wish.wishDetail.title}
                                            width="72px"
                                            height="72px"
                                            className="w-[72px] h-[72px] object-cover rounded-full mb-2"
                                        />

                                        <div>
                                            <h2 className="text-lg text-left font-semibold text-darkgreen">
                                                {wish.wishDetail.title?.trim() ||
                                                    wish.wishDetail.userDetail?.firstName?.trim() + "'s Wish" ||
                                                    'Untitled Wish'}
                                            </h2>
                                            <p className="text-sm text-gray-700 mb-2 line-clamp-2">
                                                {wish.wishDetail.description || 'No description'}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="absolute top-17 left-6 w-[32px] cursor-pointer aspect-square font-bold text-[12px] text-darkgreen rounded-full bg-white outline-1 outline-lightblue grid items-center justify-center">
                                        <span >{wish.wishDetail.totalBlessings}</span>
                                    </div>

                                    <div className='grid grid-cols-[auto_30px_30px] items-center pl-6 py-3 bg-orange/10 rounded-b-2xl border-t-1 border-orange/20'>
                                        <div className=''>Blessed {creationDate}</div>
                                        <div onClick={() => handlePinUnpinWish(wish._id, !wish.pinned)}>
                                            <img src={wish.pinned ? '/img/pin.svg' : '/img/pin-outline.svg'} width="20px" height="auto" alt={wish.pinned ? 'Pinned icon' : 'Not pinned icon'} className='cursor-pointer' />
                                        </div>
                                        {!wish.wishDetail.isGlobalWish && (<DotMenu>
                                            <div className='grid gap-2 p-3 bg-orange/10'>
                                                <Button
                                                    size="xs"
                                                    className='max-w-full w-full'
                                                    onClick={() => router.push(`/wishes/${wish._id}/edit`)}
                                                >
                                                    Edit Wish
                                                </Button>
                                                <Button
                                                    size="xs"
                                                    className='max-w-full  w-full from-error to-red-200'
                                                    onClick={() => setWishToDelete(wish._id)}
                                                >
                                                    Delete
                                                </Button>
                                                <Button
                                                    size="xs"
                                                    className='max-w-full w-full from-darkblue to-blue-400'
                                                    onClick={() => {
                                                        setWishToReport(wish._id);
                                                        setReportModalOpen(true);
                                                    }}
                                                >
                                                    Report
                                                </Button>
                                            </div>
                                        </DotMenu>)}
                                    </div>

                                    <details className="hidden mt-2 text-xs text-gray-500 whitespace-pre-wrap bg-gray-50 p-2 rounded">
                                        <summary className="cursor-pointer text-blue-600 mb-1">
                                            Raw data
                                        </summary>
                                        <pre>{JSON.stringify(wish, null, 2)}</pre>
                                    </details>
                                </li>
                            )
                        })}
                    </ul >
                </div>
                <DeleteModal visible={Boolean(wishToDelete)}
                    onCancel={() => setWishToDelete(null)}
                    onDelete={() => {
                        deleteWishById(wishToDelete)
                        setWishToDelete(null);
                    }}
                />
                <ReportModal
                    isOpen={reportModalOpen}
                    onClose={() => setReportModalOpen(false)}
                    type="WISH"
                    subjectId={wishToReport}
                />
            </div >
        </>
    );
}
