import { twMerge } from 'tailwind-merge'

export default function <PERSON><PERSON>({ children, className = "", disabled, size = "normal", stars, ...props }) {
    let style = `w-full relative text-white bg-linear-65 from-darkblue to-lightblue tracking-[0.15em] py-3 px-7 text-lg font-semibold rounded-2xl cursor-pointer transition-all duration-200 hover:scale-104 active:scale-107 ${disabled ? 'opacity-70 pointer-events-none' : ''}`
    if (size === "small" || size === "xs") {
        style = twMerge(style, "text-sm mx-auto max-w-fit py-2 px-4 hover:scale-105 active:scale-115")
    }
    if (size === "xs") {
        style = twMerge(style, "text-xs")
    }

    style = twMerge(style, className)
    return (<button {...props} disabled={disabled} className={style}>
        {children}
        {stars && (<>
            <svg className="absolute top-3 right-3 w-3 h-3 text-yellow-400 animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
            </svg>

            <svg className="absolute top-[2px] right-[5px] w-2.5 h-2.5 text-yellow-300 animate-pulse delay-200" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
            </svg>

            <svg className="absolute -top-1 -right-1 w-2 h-2 text-yellow-200 animate-pulse delay-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
            </svg>


            <svg className="absolute left-1 bottom-0 w-3 h-3 text-yellow-400 animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
            </svg>
            <svg className="absolute left-0 bottom-3 w-2 h-2 text-yellow-200 animate-pulse delay-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
            </svg>
        </>

        )}
    </button>)
}