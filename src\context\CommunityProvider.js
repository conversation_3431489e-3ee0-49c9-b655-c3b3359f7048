"use client";
import React, { createContext, useContext, useState, useEffect, useCallback } from "react";
import {
    getCommunities,
    createCommunity as createCommunityAPI,
    updateCommunity,
    deleteCommunity as deleteCommunityAPI,
    uploadMedia,
    getCommunityDetail,
    getPublicCommunityDetail,
    requestToJoinCommunity,
    respondToJoinRequest,
    getCommunityJoinRequests,
    leaveCommunity
} from "@/services/api";
import { useAuth } from "./UserProvider";

const CommunityContext = createContext();

export const CommunityProvider = ({ children }) => {
    const { accessToken, user } = useAuth();
    const [communities, setCommunities] = useState([]);
    const [currentCommunity, setCurrentCommunity] = useState(null);
    const [joinRequests, setJoinRequests] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const fetchCommunities = useCallback(async (params = {}) => {
        if (!accessToken) return;
        //console.log("👥 Fetching communities with token:", accessToken);
        setLoading(true);
        setError(null);
        try {
            const data = await getCommunities(accessToken, params);
            //console.log("✅ Communities loaded:", data);
            setCommunities(data.data || []);
        } catch (err) {
            console.error("❌ Error loading communities:", err);
            setError(err);
        } finally {
            setLoading(false);
        }
    }, [accessToken]);

    const createCommunity = useCallback(async (communityData) => {
        if (!accessToken) {
            console.warn("⚠️ Cannot create community: no access token");
            return;
        }

        try {
            const result = await createCommunityAPI(communityData, accessToken);
            console.log("🎉 Community created:", result);
            await fetchCommunities();
            return result;
        } catch (err) {
            console.error("❌ Error creating community:", err);
            throw err;
        }
    }, [accessToken, fetchCommunities]);

    const updateCommunityById = async (id, data, selectedImageFile = null) => {
        try {
            const accessToken = localStorage.getItem("accessToken");

            console.log("👥 Updating community with ID:", id, "and data:", data, 'and image:', selectedImageFile);

            // Check if selectedImageFile is a real file (not just a blob preview)
            if (selectedImageFile && selectedImageFile instanceof File) {
                console.log("📸 Uploading new image:", selectedImageFile);
                const uploadedImageUrl = await uploadMedia(selectedImageFile);
                data.image = uploadedImageUrl;
            }

            const updated = await updateCommunity(id, data, accessToken);

            setCommunities((prev) => ({
                ...prev,
                data: prev.data.map((community) =>
                    community._id === id
                        ? { ...community, communityDetail: { ...community.communityDetail, ...data } }
                        : community
                ),
            }));

            fetchCommunities();

            return updated;
        } catch (error) {
            console.error("Error updating community:", error);
            throw error;
        }
    };

    const deleteCommunityById = async (communityId) => {
        try {
            const accessToken = localStorage.getItem("accessToken");
            await deleteCommunityAPI(communityId, accessToken);

            setCommunities((prev) => ({
                ...prev,
                data: prev.data.filter((c) => c.communityDetail._id !== communityId),
            }));
            console.log("✅ Community deleted:", communityId);
            fetchCommunities();
        } catch (err) {
            console.error("Failed to delete community:", err);
            throw err;
        }
    };

    const fetchCommunityDetail = useCallback(async (communityId) => {
        if (!accessToken) return null;

        setLoading(true);
        setError(null);

        try {
            const data = await getCommunityDetail(communityId, accessToken);
            console.log("✅ Community detail loaded:", data);
            setCurrentCommunity(data.data || null);
            return data.data;
        } catch (err) {
            console.error("❌ Error loading community detail:", err);
            setError(err);
            return null;
        } finally {
            setLoading(false);
        }
    }, [accessToken]);

    const fetchPublicCommunityDetail = useCallback(async (communityId) => {
        setLoading(true);
        setError(null);

        try {
            const data = await getPublicCommunityDetail(communityId);
            console.log("✅ Public community detail loaded:", data);
            setCurrentCommunity(data.data || null);
            return data.data;
        } catch (err) {
            console.error("❌ Error loading public community detail:", err);
            setError(err);
            return null;
        } finally {
            setLoading(false);
        }
    }, []);

    const joinCommunity = useCallback(async (communityId) => {
        if (!accessToken) return null;

        setLoading(true);
        setError(null);

        try {
            const data = await requestToJoinCommunity(communityId, accessToken);
            console.log("✅ Join request sent:", data);
            return data;
        } catch (err) {
            console.error("❌ Error sending join request:", err);
            setError(err);
            return null;
        } finally {
            setLoading(false);
        }
    }, [accessToken]);

    const leaveCommunityById = useCallback(async (communityId) => {
        if (!accessToken) return null;

        setLoading(true);
        setError(null);

        try {
            const data = await leaveCommunity(communityId, accessToken);
            console.log("✅ Left community:", data);

            // Update communities list
            fetchCommunities();

            return data;
        } catch (err) {
            console.error("❌ Error leaving community:", err);
            setError(err);
            return null;
        } finally {
            setLoading(false);
        }
    }, [accessToken, fetchCommunities]);

    const fetchJoinRequests = useCallback(async (communityId, pageNo = 1, limit = 20) => {
        if (!accessToken) return null;

        setLoading(true);
        setError(null);

        try {
            const data = await getCommunityJoinRequests(communityId, accessToken, pageNo, limit);
            console.log("✅ Join requests loaded:", data);
            setJoinRequests(data.data || []);
            return data.data;
        } catch (err) {
            console.error("❌ Error loading join requests:", err);
            setError(err);
            return null;
        } finally {
            setLoading(false);
        }
    }, [accessToken]);

    const respondToJoin = useCallback(async (communityId, userId, accept) => {
        if (!accessToken) return null;

        setLoading(true);
        setError(null);

        try {
            const data = await respondToJoinRequest(communityId, userId, accept, accessToken);
            console.log("✅ Responded to join request:", data);

            // Refresh join requests
            fetchJoinRequests(communityId);

            return data;
        } catch (err) {
            console.error("❌ Error responding to join request:", err);
            setError(err);
            return null;
        } finally {
            setLoading(false);
        }
    }, [accessToken, fetchJoinRequests]);

    useEffect(() => {
        //console.log("👥 Access token changed:", accessToken);
        if (accessToken) {
            fetchCommunities();
        }
    }, [accessToken, fetchCommunities]);

    return (
        <CommunityContext.Provider
            value={{
                communities,
                currentCommunity,
                joinRequests,
                loading,
                error,
                fetchCommunities,
                createCommunity,
                updateCommunityById,
                deleteCommunityById,
                fetchCommunityDetail,
                fetchPublicCommunityDetail,
                joinCommunity,
                leaveCommunityById,
                fetchJoinRequests,
                respondToJoin
            }}
        >
            {children}
        </CommunityContext.Provider>
    );
};

export const useCommunities = () => useContext(CommunityContext);
