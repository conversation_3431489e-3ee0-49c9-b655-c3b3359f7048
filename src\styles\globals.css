@tailwind base;
@tailwind components;
@tailwind utilities;

/* Heading */
@font-face {
  font-family: "Calistoga";
  src: url("/fonts/calistoga/Calistoga-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
}
/* Subheading and body */
@font-face {
  font-family: "Aileron";
  src: url("/fonts/aileron/Aileron-Regular.otf") format("opentype");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: "Aileron";
  src: url("/fonts/aileron/Aileron-Italic.otf") format("opentype");
  font-weight: 400;
  font-style: italic;
}
@font-face {
  font-family: "Aileron";
  src: url("/fonts/aileron/Aileron-SemiBold.otf") format("opentype");
  font-weight: 600;
  font-style: normal;
}
@font-face {
  font-family: "Aileron";
  src: url("/fonts/aileron/Aileron-Bold.otf") format("opentype");
  font-weight: 700;
  font-style: normal;
}

:root {
  --font-heading: "Calistoga", serif;
  --font-subheading: "Aileron", sans-serif;
  --font-body: "Aileron", sans-serif;

  --font-sans: "Aileron", sans-serif;
  --font-mono: var(--font-geist-mono);

  --color-background: #ffffff;
  --color-foreground: #171717;

  --color-beige: #faf5e8;
  --color-lightblue: #6a8798;
  --color-fadedblue: #ecf4f4;
  --color-lightgreen: #cfdfde; /* TODO: delete old color */
  --color-darkblue: #1a2d39;
  --color-darkgreen: #0e5d5c;
  --color-orange: #e5b07c;
  --color-darkorange: #d6a087;
  --color-beige: #f3e9dd;
  --color-lightbeige: #fbf8f4;

  --color-error: #c20114;

  --animate-fadein: fadeIn 0.6s cubic-bezier(0.26, 0.53, 0.74, 1.48) forwards;
  --animate-fadeOut: fadeOut 0.5s cubic-bezier(0.26, 0.53, 0.74, 1.48) forwards;

  --animate-slide-left-out: slide-left-out 0.5s ease-in forwards;
  --animate-slide-left-in: slide-left-in 0.5s ease-out forwards;
  --animate-slide-right-out: slide-right-out 0.5s ease-in forwards;
  --animate-slide-right-in: slide-right-in 0.5s ease-out forwards;

  --animate-fade-in-blur: fade-in-blur 0.5s ease-out forwards;
  --animate-fade-out-blur: fade-out-blur 0.5s ease-in forwards;
  --animate-grow: grow 0.5s ease forwards;
  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  @keyframes fadeOut {
    0% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
  @keyframes slide-left-out {
    0% {
      transform: translateX(0);
      opacity: 1;
      filter: blur(0);
    }
    100% {
      transform: translateX(-100%);
      opacity: 0;
      filter: blur(8px);
    }
  }

  @keyframes slide-left-in {
    0% {
      transform: translateX(100%);
      opacity: 0;
      filter: blur(8px);
    }
    100% {
      transform: translateX(0);
      opacity: 1;
      filter: blur(0);
    }
  }

  @keyframes slide-right-out {
    0% {
      transform: translateX(0);
      opacity: 1;
      filter: blur(0);
    }
    100% {
      transform: translateX(100%);
      opacity: 0;
      filter: blur(8px);
    }
  }

  @keyframes slide-right-in {
    0% {
      transform: translateX(-100%);
      opacity: 0;
      filter: blur(8px);
    }
    100% {
      transform: translateX(0);
      opacity: 1;
      filter: blur(0);
    }
  }
  @keyframes fade-in-blur {
    0% {
      opacity: 0;
      filter: blur(8px);
    }
    100% {
      opacity: 1;
      filter: blur(0);
    }
  }
  @keyframes fade-out-blur {
    0% {
      opacity: 1;
      filter: blur(0);
    }
    100% {
      opacity: 0;
      filter: blur(8px);
    }
  }
  @keyframes grow {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.2);
    }
    100% {
      transform: scale(1.1);
    }
  }
}

@layer components {
  /* .animate-delay-500 {
    animation-delay: 0.5s;
  } */
  .bg-mesh {
    background-color: hsla(0, 0%, 100%, 0.5);
    background-image: radial-gradient(
        at 35% 20%,
        hsla(176, 100%, 84%, 0.5) 0px,
        transparent 50%
      ),
      radial-gradient(at 63% 58%, hsla(43, 100%, 94%, 0.5) 0px, transparent 50%),
      radial-gradient(at 0% 0%, hsla(216, 100%, 87%, 0.5) 0px, transparent 50%);
  }
  html {
    overflow-x: hidden;
  }

  .drop-shadow-custom {
    filter: drop-shadow(0px 2px 4px var(--color-orange))
      drop-shadow(0px 2px 4px var(--color-orange))
      drop-shadow(0px 0px 4px var(--color-orange));
  }
}

/* LoadingIndicator */
@keyframes ldio {
  0% {
    top: 96px;
    left: 96px;
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    top: 56px;
    left: 56px;
    width: 80px;
    height: 80px;
    opacity: 0;
  }
}

.ldio div {
  position: absolute;
  border-width: 4px;
  border-style: solid;
  opacity: 1;
  border-radius: 50%;
  animation: ldio 1.53s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}
.ldio div:nth-child(1) {
  border-color: #3a4669;
  animation-delay: 0s;
}
.ldio div:nth-child(2) {
  border-color: #d1ddef;
  animation-delay: -0.76;
}
.loadingio-spinner-ripple {
  width: 200px;
  height: 200px;
  display: inline-block;
  overflow: hidden;
  background: #ffffff;
}
.ldio {
  width: 100%;
  height: 100%;
  position: relative;
  transform: translateZ(0) scale(1);
  backface-visibility: hidden;
  transform-origin: 0 0; /* see note above */
}
.ldio div {
  box-sizing: content-box;
}

/* Leaflet cluster css override */
.leaflet-cluster-anim .leaflet-marker-icon,
.leaflet-cluster-anim .leaflet-marker-shadow {
  -webkit-transition: -webkit-transform 0.3s ease-out, opacity 0.3s ease-in;
  -moz-transition: -moz-transform 0.3s ease-out, opacity 0.3s ease-in;
  -o-transition: -o-transform 0.3s ease-out, opacity 0.3s ease-in;
  transition: transform 0.3s ease-out, opacity 0.3s ease-in;
}

.leaflet-cluster-spider-leg {
  /* stroke-dashoffset (duration and function) should match with leaflet-marker-icon transform in order to track it exactly */
  -webkit-transition: -webkit-stroke-dashoffset 0.3s ease-out,
    -webkit-stroke-opacity 0.3s ease-in;
  -moz-transition: -moz-stroke-dashoffset 0.3s ease-out,
    -moz-stroke-opacity 0.3s ease-in;
  -o-transition: -o-stroke-dashoffset 0.3s ease-out,
    -o-stroke-opacity 0.3s ease-in;
  transition: stroke-dashoffset 0.3s ease-out, stroke-opacity 0.3s ease-in;
}

.marker-cluster-small {
  background-color: #2bd17e !important;
}
.marker-cluster-small div {
  background-color: #2bd17e9a !important;
}

.marker-cluster-medium {
  background-color: #43b6aa !important;
}
.marker-cluster-medium div {
  background-color: #43b6ab8b !important;
}

.marker-cluster-large {
  background-color: #5b9bd5 !important;
}
.marker-cluster-large div {
  background-color: #5b9ad595 !important;
}

/* IE 6-8 fallback colors */
.leaflet-oldie .marker-cluster-small {
  background-color: #2bd17e !important;
}
.leaflet-oldie .marker-cluster-small div {
  background-color: #2bd17e !important;
}

.leaflet-oldie .marker-cluster-medium {
  background-color: #43b6aa !important;
}
.leaflet-oldie .marker-cluster-medium div {
  background-color: #43b6aa !important;
}

.leaflet-oldie .marker-cluster-large {
  background-color: #5b9bd5 !important;
}
.leaflet-oldie .marker-cluster-large div {
  background-color: #5b9bd5 !important;
}

.marker-cluster {
  background-clip: padding-box;
  border-radius: 20px;
}
.marker-cluster div {
  width: 30px;
  height: 30px;
  margin-left: 5px;
  margin-top: 5px;

  text-align: center;
  border-radius: 15px;
  font: 12px "Helvetica Neue", Arial, Helvetica, sans-serif;
}
.marker-cluster span {
  line-height: 30px;
}
