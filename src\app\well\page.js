'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useBlessings } from '@/context/BlessingsProvider';
import { useAuth } from '@/context/UserProvider';
import ReportBlessingModal from '@/components/ReportBlessingModal';
import UnderwaterBackground from '@/components/UnderwaterBackground';
import Divider from '@/components/Divider';
import getImageUrl from '@/services/imageURL';
import { formatDistanceToNow } from 'date-fns';
import Button from '@/components/Button';

export default function WellPage() {
    const router = useRouter();
    const { getWell, wellData, loadingWell } = useBlessings();
    const { user } = useAuth();
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [reportModalOpen, setReportModalOpen] = useState(false);
    const [selectedBlessingId, setSelectedBlessingId] = useState(null);
    const [leaveAnimation, setLeaveAnimation] = useState(false)

    useEffect(() => {
        loadInitialData();
    }, []);

    const loadInitialData = async () => {
        try {
            const data = await getWell(1, 10);
            setHasMore(data && data.length >= 10);
        } catch (error) {
            console.error("Error loading well data:", error);
        }
    };

    const loadMoreData = async () => {
        if (!hasMore || loadingWell) return;

        try {
            const nextPage = page + 1;
            const data = await getWell(nextPage, 10);
            setPage(nextPage);
            setHasMore(data && data.length >= 10);
        } catch (error) {
            console.error("Error loading more well data:", error);
        }
    };

    const handleReportBlessing = (blessingId) => {
        setSelectedBlessingId(blessingId);
        setReportModalOpen(true);
    };

    const formatDate = (timestamp) => {
        if (!timestamp) return 'Unknown date';
        const date = new Date(timestamp);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    if (loadingWell && wellData.length === 0) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (!wellData || wellData.length === 0) {
        return (
            <div className="p-4 text-center">
                <h1 className="text-2xl font-bold mb-6">The Well</h1>
                <div className="bg-blue-50 p-6 rounded-lg shadow-sm">
                    <p className="text-blue-800">No blessings have been added to the well yet.</p>
                    <p className="mt-2 text-blue-600">Perform blessings on wishes to see them appear here.</p>
                    <button
                        onClick={() => router.push('/wishes')}
                        className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition"
                    >
                        Go to Wishes
                    </button>
                </div>
            </div>
        );
    }

    return (
        <>
            <UnderwaterBackground />

            <div className={`md:py-17 h-full overflow-x-hidden opacity-0 animate-fade-in-blur ${leaveAnimation ? 'animate-slide-left-out overflow-hidden' : 'overflow-y-auto'}`}>
                <div className="p-6 max-w-3xl relative pb-20 md:pb-auto min-h-full md:min-h-auto md:mt-10 mx-auto backdrop-blur-md shadow-md bg-transparent md:bg-lightbeige md:rounded-2xl">
                    <h1 className="text-5xl mt-4 mb-8 font-heading text-center text-darkgreen">The Well</h1>
                    <Divider className='my-9 md:my-6' />
                    <div className="">
                        <p className="text-darkgreen mb-8 text-center">
                            A collection of all blessings performed by the community
                        </p>
                        <ul className="grid md:grid-cols-2 items-start gap-4">
                            {(wellData || []).map((blessing) => {
                                const imageUrl = getImageUrl(blessing.userDetail?.profilePicture);
                                const creationDate = formatDistanceToNow(new Date(blessing.created), { addSuffix: true });
                                return (
                                    <li
                                        key={blessing._id}
                                        className="pt-6 rounded-2xl shadow-sm bg-white relative animate-fade-in-blur"
                                    >
                                        <div className='grid grid-cols-[72px_auto] gap-5 px-6 pb-3 cursor-pointer'

                                        >

                                            {imageUrl && (<img
                                                src={imageUrl}
                                                alt={blessing.userDetail?.name || 'Anonymous'}
                                                width="72px"
                                                height="72px"
                                                className="w-[72px] h-[72px] object-cover rounded-full mb-2"
                                            />)}
                                            {!imageUrl && <div></div>}

                                            <div>
                                                <h2 className="text-lg text-left font-semibold text-darkgreen">
                                                    {blessing.userDetail?.name || 'Anonymous'}
                                                </h2>
                                                <p className="text-sm text-gray-700 mb-2 line-clamp-2">
                                                    {blessing.notes || ''}
                                                </p>
                                            </div>
                                        </div>

                                        <div className='grid grid-cols-[auto_30px_30px] items-start  pl-6 py-3 bg-orange/10 rounded-b-2xl border-t-1 border-orange/20'>
                                            <div className=''>Blessed {creationDate}</div>
                                        </div>
                                    </li>
                                )
                            })}
                        </ul >
                        <div className="space-y-6 hidden">
                            {wellData.map((blessing) => (
                                <div key={blessing._id} className="bg-white rounded-lg shadow-md overflow-hidden">
                                    <div className="p-4 border-b">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center overflow-hidden mr-3">
                                                    {blessing.userDetail?.profilePicture ? (
                                                        <img
                                                            src={blessing.userDetail.profilePicture}
                                                            alt={blessing.userDetail.name || 'User'}
                                                            className="w-full h-full object-cover"
                                                        />
                                                    ) : (
                                                        <span className="text-blue-500 font-bold">
                                                            {blessing.userDetail?.name?.charAt(0) || '?'}
                                                        </span>
                                                    )}
                                                </div>
                                                <div>
                                                    <p className="font-medium">{blessing.userDetail?.name || 'Anonymous'}</p>
                                                    <p className="text-xs text-gray-500">{formatDate(blessing.created)}</p>
                                                </div>
                                            </div>

                                            <div className="text-sm text-gray-500">
                                                {blessing.type === 'AUDIO' ? (
                                                    <span className="flex items-center">
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                                                        </svg>
                                                        Audio Blessing
                                                    </span>
                                                ) : blessing.type === 'TEXT' ? (
                                                    <span className="flex items-center">
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                        </svg>
                                                        Text Blessing
                                                    </span>
                                                ) : (
                                                    <span className="flex items-center">
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                        </svg>
                                                        Listened Only
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                    <div className="p-4">
                                        {blessing.notes && (
                                            <p className="text-gray-700 mb-4">{blessing.notes}</p>
                                        )}

                                        {blessing.audio && (
                                            <div className="mb-4">
                                                <audio controls className="w-full">
                                                    <source src={blessing.audio} type="audio/mpeg" />
                                                    Your browser does not support the audio element.
                                                </audio>
                                            </div>
                                        )}

                                        <div className="flex items-center text-xs text-gray-500 mt-2">
                                            <div className="flex items-center mr-4">
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                                {blessing.location?.city}, {blessing.location?.state}, {blessing.location?.country}
                                            </div>

                                            {blessing.userDetail?._id !== user?._id && (
                                                <button
                                                    onClick={() => handleReportBlessing(blessing._id)}
                                                    className="text-red-500 hover:text-red-700 flex items-center"
                                                >
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                                    </svg>
                                                    Report
                                                </button>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        {hasMore && (
                            <div className="mt-6 text-center max-w-[250px] mx-auto">
                                <Button
                                    onClick={loadMoreData}
                                    disabled={loadingWell}
                                >
                                    {loadingWell ? 'Loading...' : 'Load More'}
                                </Button>
                            </div>
                        )}

                        <ReportBlessingModal
                            isOpen={reportModalOpen}
                            onClose={() => setReportModalOpen(false)}
                            blessingId={selectedBlessingId}
                        />
                    </div>
                </div>
            </div >
        </>
    );
}