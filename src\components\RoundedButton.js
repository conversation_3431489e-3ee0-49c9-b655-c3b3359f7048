export default function RoundedButton({ children, className = "", ...props }) {
    return (<button {...props} className={` w-14 h-14 ${className.includes('bg-') ? '' : 'bg-linear-65 from-darkblue to-lightblue'} ${className.includes('text-') ? '' : 'text-white'} text-4xl ${className.includes('font-') ? '' : 'font-semibold'} rounded-full cursor-pointer transition-all duration-200 hover:scale-104 active:scale-107 ${className}`}>
        {children}
    </button>)
}