import { twMerge } from 'tailwind-merge'
export default function Modal({ visible, children, className = "" }) {
    const style = twMerge("p-6 max-w-xl max-h-dvh overflow-y-auto overflow-x-hidden md:min-h-auto md:mt-10 mx-auto backdrop-blur-md shadow-md bg-beige rounded-2xl", className)

    return (<div className={`absolute top-0 left-0 z-1 h-dvh w-full grid items-center justify-center overflow-y-hidden duration-300 transition-all bg-gray-500/20 ${visible ? 'translate-x-0 opacity-100' : 'translate-x-0 md:translate-x-5 opacity-0 pointer-events-none'}`}>
        <div className={style}>
            {children}
        </div>
    </div>)
}