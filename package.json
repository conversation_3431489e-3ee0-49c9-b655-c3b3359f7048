{"name": "wishwell-pwa", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.758.0", "@headlessui/react": "^2.2.0", "@react-google-maps/api": "^2.20.6", "@react-oauth/google": "^0.12.1", "@stripe/react-stripe-js": "^3.3.0", "@stripe/stripe-js": "^5.8.0", "aws-sdk": "^2.1692.0", "axios": "^1.8.1", "date-fns": "^3.6.0", "firebase": "^11.4.0", "jotai": "^2.12.1", "leaflet": "^1.9.4", "localforage": "^1.10.0", "lottie-react": "^2.4.1", "next": "15.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-image-crop": "^11.0.7", "react-leaflet": "^5.0.0", "react-leaflet-markercluster": "^5.0.0-rc.0", "react-select": "^5.10.1", "react-spinners": "^0.15.0", "react-toastify": "^11.0.5", "react-world-flags": "^1.6.0", "socket.io-client": "^4.8.1", "swr": "^2.3.2", "tailwind-merge": "^3.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.2.0", "postcss": "^8.4.49", "tailwindcss": "^4"}}