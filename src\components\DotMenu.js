import { useState, useRef } from "react";
import Button from "./Button";
import { useOutsideClick } from "@/utils/useOuterClick";
export default function DotMenu({ children }) {
    const ref = useRef()
    const [isOpen, setIsOpen] = useState(false)
    useOutsideClick(ref.current, () => {
        if (isOpen) setIsOpen(false)
    }, [isOpen]);
    return (
        <div
            onClick={(e) => {
                e.stopPropagation();
            }}
            className="relative"
        >
            <img src="/img/dots-vertical.svg" width="20px"
                className='cursor-pointer hover:scale-110 active:scale-120 transition-all duration-200'
                alt="Action menu icon for the wish."
                onClick={() => setIsOpen(!isOpen)}
            />
            <div ref={ref} className={`absolute bottom-6 right-6 rounded-2xl min-w-32 bg-white shadow-lg ${isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'} transition-all duration-200 overflow-hidden`}
                onClick={() => setIsOpen(false)}
            >
                {children}
            </div>
        </div>
    )
}