export default function AuthFrame({ children }) {
    return (
        <div className="relative h-dvh bg-[url(/img/ripple.webp)] bg-cover bg-center bg-no-repeat">
            <div className="flex items-start md:items-center justify-center h-dvh overflow-y-auto bg-darkgreen/90 animate-fadein animate-delay-500 shadow-[inset_0_0_100px_0_var(--color-darkblue)]">
                <div className="w-full md:w-auto md:px-0 px-4 pb-20">
                    <h1 className="text-4xl my-7 tracking-wider md:mt-0 text-center font-heading text-beige">WishWell</h1>
                    <div className="p-6 w-full md:w-[375px] text-center shadow-2xl shadow-darkblue/30 rounded-2xl bg-beige">
                        {children}
                    </div>
                </div>
            </div>
        </div>
    );
}
