import { useEffect, useState } from "react";
import { useLayout } from "@/context/LayoutProvider";
import { useAuth } from "@/context/UserProvider";
import getImageUrl from "@/services/imageURL"; // 👈 make sure this is the updated function
import { usePathname, useSearchParams, useRouter } from 'next/navigation'
export default function NavigationBar() {
    const { userProfile } = useAuth();
    const [profileImageUrl, setProfileImageUrl] = useState(null);
    const router = useRouter()
    const pathname = usePathname()
    const searchParams = useSearchParams()
    const { showBottomNav, activeTab, setActiveTab } = useLayout();

    useEffect(() => {
        ["wishes", "dashboard", "map", "well"].forEach(pageName => {
            if (pathname.includes(`/${pageName}`)) {
                setActiveTab(pageName)
            }
        })
    }, [pathname, searchParams, setActiveTab])

    useEffect(() => {
        if (userProfile?.data?.profilePicture) {
            const processedUrl = getImageUrl(userProfile.data.profilePicture);
            console.log("✅ Processed profile image URL:", processedUrl);
            setProfileImageUrl(processedUrl);
        }
    }, [userProfile]);



    return (<div className={`transition-all duration-300 fixed md:fixed bottom-0 md:bottom-auto  md:top-4 left-0 grid items-end md:items-center w-screen md:grid-cols-[auto_auto_auto] md:px-4 md:justify-between ${showBottomNav ? 'opacity-100' : 'opacity-0'}`}>
        <div className="bg-beige rounded-2xl p-4 hidden md:block transition-all duration-200 hover:scale-105 active:scale-110 cursor-pointer"
            onClick={() => router.push("/dashboard")}
        >
            <img src="/img/logo-color.png" width="50px" alt="Wish Well logo." />
        </div>
        <div className="bg-beige rounded-t-4xl md:rounded-2xl grid p-4 grid-cols-[auto_auto_auto_auto] gap-6 md:gap-10">

            <button className={`cursor-pointer ${activeTab === 'dashboard' ? 'text-darkgreen' : 'text-gray-500'}`}
                onClick={() => { router.push("/dashboard"); setActiveTab("dashboard") }}>
                <img src={`/img/nav-icon-dashboard${activeTab === 'dashboard' ? '-active.svg' : '.svg'}`} width="auto" alt="Dashboard icon"
                    className={`h-9 md:block ${activeTab === 'dashboard' ? 'bg-[#f1a2a3] rounded-full invert' : ''}`}
                />
            </button>
            <button
                className={`cursor-pointer ${activeTab.includes("wishes") ? 'text-darkgreen' : 'text-gray-500'}`}
                onClick={() => { router.push("/wishes"); setActiveTab("wishes") }}>
                <img src={`/img/nav-icon-wishes${activeTab === 'wishes' ? '-active.svg' : '.svg'}`} width="auto" alt="Wishes icon"
                    className={`h-9 md:block ${activeTab === 'wishes' ? 'bg-[#f1a2a3] rounded-full invert' : ''}`}
                />
            </button>
            <button
                className={`cursor-pointer ${activeTab.includes("map") ? 'text-darkgreen' : 'text-gray-500'}`}
                onClick={() => { router.push("/map"); setActiveTab("map") }}>
                <img src={`/img/nav-icon-impact${activeTab === 'map' ? '-active.svg' : '.svg'}`} width="auto" alt="Impact icon"
                    className={`h-9 md:block ${activeTab === 'map' ? 'bg-[#f1a2a3] rounded-full invert' : ''}`}
                />
            </button>
            <button
                className={`cursor-pointer ${activeTab.includes("well") ? 'text-darkgreen' : 'text-gray-500'}`}
                onClick={() => { router.push("/well"); setActiveTab("well") }}>
                <img src={`/img/nav-icon-well${activeTab === 'well' ? '-active.svg' : '.svg'}`} width="auto" alt="Well icon"
                    className={`h-9 md:block ${activeTab === 'well' ? 'bg-[#f1a2a3] rounded-full invert' : ''}`}
                />
            </button>
        </div>
        <div className="p-4 hidden md:block cursor-pointer transition-all duration-200 hover:scale-110 active:scale-115" onClick={() => { router.push("/account") }}>
            {profileImageUrl && <img src={profileImageUrl} className="w-10 h-10 rounded-full object-cover border border-gray-300" alt="Profile image" />}
        </div>
    </ div>)
}