'use client';

import '../styles/globals.css';
import 'leaflet/dist/leaflet.css';
import 'react-leaflet-cluster/lib/assets/MarkerCluster.css';
import 'react-leaflet-cluster/lib/assets/MarkerCluster.Default.css';
import { AuthProvider } from '@/context/UserProvider';
import AuthGuard from '@/components/AuthGuard';
import { usePathname } from 'next/navigation';
import { CommunityProvider } from '@/context/CommunityProvider';
import { WishProvider } from '@/context/WishProvider';
import { BlessingProvider } from '@/context/BlessingsProvider';
import { CompassionMapProvider } from '@/context/CompassionMapProvider';
import { LayoutProvider } from '@/context/LayoutProvider';
import { FriendsProvider } from '@/context/FriendsProvider';
import NavigationBar from '@/components/NavigationBar';

export default function RootLayout({ children }) {
    const pathname = usePathname();
    const publicRoutes = ['/login', '/signup', '/forgot-password'];

    return (
        <html lang="en">
            <body className='bg-beige h-dvh overflow-x-hidden overflow-y-hidden touch-none select-none shadow-[inset_0_0_100px_0_rgba(255,255,255,0.8)]'>
                <AuthProvider>
                    {publicRoutes.includes(pathname) ? (
                        children
                    ) : (
                        <AuthGuardWrapper>{children}</AuthGuardWrapper>
                    )}
                </AuthProvider>
            </body>
        </html>
    );
}

/**
 * Wrap authenticated routes with the CommunityProvider
 */
function AuthGuardWrapper({ children }) {
    return (
        <AuthGuard>
            <LayoutProvider>
                <CommunityProvider>
                    <WishProvider>
                        <BlessingProvider>
                            <FriendsProvider>
                                <CompassionMapProvider>
                                    {children}
                                    <NavigationBar />
                                </CompassionMapProvider>
                            </FriendsProvider>
                        </BlessingProvider>
                    </WishProvider>
                </CommunityProvider>
            </LayoutProvider>
        </AuthGuard>
    );
}
