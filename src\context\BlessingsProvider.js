import { createContext, useContext, useState, useCallback, useEffect } from "react";
import {
    getBlessingLibrary,
    performBlessing as performBlessing<PERSON><PERSON>,
    getWellData,
    giveGratitude as giveGratitudeAPI,
    reportBlessing as reportBlessing<PERSON>I,
    reportGratitude as reportGratitudeAPI
} from "@/services/api";
import getUserLocation from "@/services/getUserLocation";
import { useAuth } from "@/context/UserProvider";

const BlessingContext = createContext();

export const BlessingProvider = ({ children }) => {
    const [currentBlessing, setCurrentBlessing] = useState(null);
    const [meditationBlessings, setMeditationBlessings] = useState([]);
    const [prayerBlessings, setPrayerBlessings] = useState([]);
    const [loadingBlessings, setLoadingBlessings] = useState(false);
    const [loadingWell, setLoadingWell] = useState(false);
    const [wellData, setWellData] = useState([]);
    const [error, setError] = useState(null);
    const { accessToken, userProfile } = useAuth();

    useEffect(() => {
        if (!accessToken) return;

        const loadAllBlessings = async () => {
            setLoadingBlessings(true);
            try {
                const [meditations, prayers] = await Promise.all([
                    getBlessingLibrary(accessToken, "MEDITATION"),
                    getBlessingLibrary(accessToken, "PRAYER"),
                ]);
                setMeditationBlessings(meditations.data || []);
                setPrayerBlessings(prayers.data || []);
                /*console.log("✅ Blessings loaded successfully:", {
                    meditationBlessings: meditations.data,
                    prayerBlessings: prayers.data,
                });*/
            } catch (err) {
                console.error("❌ Error preloading blessings:", err);
                setError(err);
            } finally {
                setLoadingBlessings(false);
            }
        };

        loadAllBlessings();
    }, [accessToken]);

    const performBlessing = async ({ wishId, blessingId, notes = '', type = "TEXT", audioURL = null }) => {
        //console.log("🔄 Performing blessing with IDs:", wishId, blessingId);
        if (!accessToken) throw new Error("No access token");
        if (!wishId || !blessingId) throw new Error("Missing wishId or blessingId");

        const blessing =
            meditationBlessings.find((b) => b._id === blessingId) ||
            prayerBlessings.find((b) => b._id === blessingId);

        if (!blessing) throw new Error("Blessing not found");

        const listenAudio = blessing.audioFile;
        const listenDuration = 1;

        const blessingType = audioURL ? "AUDIO" : type;

        const payload = {
            wishId,
            listenBlessingId: blessingId,
            listenAudio,
            listenDuration,
            type: blessingType,
            atSignup: false,
        };

        if (userProfile?.data?.locationSharing) {
            try {
                const location = await getUserLocation();
                if (location) {
                    payload.location = location;
                } else {
                    console.warn("📍 Location not available, proceeding without it.");
                }
            } catch (err) {
                console.warn("⚠️ Failed to get location:", err);
            }
        }

        if (notes !== '') {
            payload.notes = notes;
        }

        if(audioURL) {
            payload.audio = audioURL;
            payload.audioLength = 1;
        }

        return await performBlessingAPI(accessToken, payload);
    };

    const giveGratitude = async ({ wishId, blessingId = null, notes = '', type = "TEXT", gratitudeType = "PERSONALISED", audioURL = null }) => {
        if (!accessToken) throw new Error("No access token");
        if (!wishId) throw new Error("Missing wishId");

        const gratAudio = audioURL ? "AUDIO" : type;

        const payload = {
            wishId,
            type: gratAudio,
            gratitudeType,
        };

        if (blessingId) {
            payload.blessingId = blessingId;
        }

        if (notes !== '') {
            payload.notes = notes;
        }

        if(audioURL) {
            payload.audio = audioURL;
            payload.audioLength = 1;
        }

        if (userProfile?.data?.locationSharing) {
            try {
                const location = await getUserLocation();
                if (location) {
                    payload.location = location;
                } else {
                    console.warn("📍 Location not available, proceeding without it.");
                }
            } catch (err) {
                console.warn("⚠️ Failed to get location:", err);
            }
        }

        return await giveGratitudeAPI(accessToken, payload);
    };

    const getWell = async (pageNo = 1, limit = 100) => {
        if (!accessToken) throw new Error("No access token");
        setLoadingWell(true);
        try {
            const response = await getWellData(accessToken, pageNo, limit);
            if (response.data) {
                if(pageNo === 1) {
                    setWellData(response.data);
                }
                else {
                    setWellData((prev) => [...prev, ...response.data]);
                }
            }
            setLoadingWell(false);
            return response.data;
        } catch (err) {
            console.error("❌ Error fetching well data:", err);
            throw err;
        }
    }

    const reportBlessing = async (blessingId, reason, description = "") => {
        if (!accessToken) throw new Error("No access token");
        try {
            const response = await reportBlessingAPI(accessToken, blessingId, reason, description);
            console.log("✅ Blessing reported successfully:", response);
            return response;
        } catch (err) {
            console.error("❌ Error reporting blessing:", err);
            throw err;
        }
    };

    const reportGratitude = async (gratitudeId, reason, description = "") => {
        if (!accessToken) throw new Error("No access token");
        try {
            const response = await reportGratitudeAPI(accessToken, gratitudeId, reason, description);
            console.log("✅ Gratitude reported successfully:", response);
            return response;
        } catch (err) {
            console.error("❌ Error reporting gratitude:", err);
            throw err;
        }
    };

    return (
        <BlessingContext.Provider
            value={{
                currentBlessing,
                meditationBlessings,
                prayerBlessings,
                loadingBlessings,
                loadingWell,
                wellData,
                error,
                setCurrentBlessing,
                performBlessing,
                getWell,
                giveGratitude,
                reportBlessing,
                reportGratitude
            }}
        >
            {children}
        </BlessingContext.Provider>
    );
};

export const useBlessings = () => useContext(BlessingContext);
