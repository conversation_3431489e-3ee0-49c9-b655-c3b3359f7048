'use client';

import { createContext, useContext, useEffect, useState, useCallback } from 'react';
import {
    getGlobalWishes,
    getRequestedWishes,
    getReceivedWishes,
    createWish as createWish<PERSON><PERSON>,
    deleteWish as deleteWish<PERSON><PERSON>,
    editWish as editWish<PERSON><PERSON>,
    getWishDetails,
    getBlessingsByWishId,
    getPinnedWishes,
    pinUnpinWish
} from '@/services/api';
import { useAuth } from '@/context/UserProvider';

const WishContext = createContext();

export const WishProvider = ({ children }) => {
    const { accessToken, user } = useAuth();

    const [globalWishes, setGlobalWishes] = useState([]);
    const [requestedWishes, setRequestedWishes] = useState([]);
    const [receivedWishes, setReceivedWishes] = useState([]);

    const [wishDetails, setWishDetails] = useState(null);
    const [wishBlessings, setWishBlessings] = useState(null);

    const [pinnedWishes, setPinnedWishes] = useState([]);

    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const [filters, setFilters] = useState({
        searchKey: '',
        communityId: [],
        pageNo: 1,
        limit: 20,
    });

    const fetchAllWishes = useCallback(async () => {
        if (!accessToken || !user?._id) return;

        setLoading(true);
        setError(null);

        try {
            console.log("💡 Fetching requested wishes with:", {
                userId: user?._id,
                filters
            });
            const [globalRes, requestedRes, receivedRes] = await Promise.all([
                getGlobalWishes(accessToken, filters),
                getRequestedWishes(accessToken, user._id, filters),
                getReceivedWishes(accessToken, user._id, filters),
            ]);

            setGlobalWishes(globalRes.data || []);
            setRequestedWishes(requestedRes.data || []);
            setReceivedWishes(receivedRes.data || []);
        } catch (err) {
            console.error('❌ WishProvider error:', err);
            setError(err.message || 'Failed to fetch wishes');
        } finally {
            setLoading(false);
        }
    }, [accessToken, user?._id, filters]);

    const createWish = async (wishData) => {
        try {
            if (!accessToken) throw new Error("No access token");

            const newWish = await createWishAPI(accessToken, wishData);
            console.log("✅ Wish created:", newWish);

            await fetchAllWishes();
            return newWish;
        } catch (err) {
            console.error("❌ Failed to create wish:", err);
            throw err;
        }
    };

    const deleteWishById = async (wishId) => {
        if (!user || !accessToken) return;
        try {
            await deleteWishAPI(accessToken, wishId);
            await fetchAllWishes();
        } catch (err) {
            console.error("Error deleting wish:", err);
            throw err;
        }
    };

    const editWishById = async (wishData) => {
        if (!accessToken || !wishData?.wishId) throw new Error("Missing required data");

        try {
            const result = await editWishAPI(accessToken, wishData);
            console.log("✏️ Wish updated:", result);

            await fetchAllWishes();
            return result;
        } catch (err) {
            console.error("❌ Error updating wish:", err);
            throw err;
        }
    };

    const fetchWishDetails = useCallback(async (wishId) => {
        if (!accessToken || !wishId) return;

        try {
            const res = await getWishDetails(accessToken, wishId);
            setWishDetails(res?.data || null);
            if(res?.data) {
                await getWishBlessings(res.data._id);
            }
        } catch (err) {
            console.error('❌ Error loading wish details:', err);
            setWishDetails(null);
        }
    }, [accessToken]);

    const getWishBlessings = async (wishId) => {
        if (!accessToken || !wishId) return;

        try {
            const res = await getBlessingsByWishId(accessToken, wishId);
            setWishBlessings(res?.data || null);
        } catch (err) {
            console.error('❌ Error loading wish blessings:', err);
            setWishBlessings(null);
        }
    };

    const fetchPinnedWishes = async () => {
        if (!accessToken || !user?._id) return;

        try {
            const res = await getPinnedWishes(accessToken, user._id);
            setPinnedWishes(res.data || []);
        } catch (err) {
            console.error('❌ Error loading pinned wishes:', err);
            setPinnedWishes([]);
        }
    }

    const pinUnpinWishById = async (wishId, pin = true) => {
        if (!accessToken || !wishId) return;

        try {
            const addRemove = pin ? "ADD" : "REMOVE";
            if (pin) {
                console.log("💡 Pinning wish:", wishId);
            }
            else {
                console.log("💡 Unpinning wish:", wishId);
            }
            const res = await pinUnpinWish(accessToken, wishId, addRemove);
            if(res?.type == "LIMIT_EXCEEDED") {
                console.log("❌ Limit exceeded!");
                alert("You have reached the limit of pinned wishes. Please unpin one before pinning another.");
                return res;
            }
            console.log("✅ Wish pinned/unpinned:", res);
            await fetchPinnedWishes();
        } catch (err) {
            console.log('❌ Error pinning/unpinning wish:', err);
            if(err.response?.data.type === "LIMIT_EXCEEDED") {
                console.log("❌ Limit exceeded!");
                return err.response?.data;
            } else {
                throw err;
            }
            
            
        }
    }

    useEffect(() => {
        fetchAllWishes();
    }, [fetchAllWishes]);

    return (
        <WishContext.Provider
            value={{
                loading,
                error,
                filters,
                setFilters,
                globalWishes,
                requestedWishes,
                receivedWishes,
                refetchAllWishes: fetchAllWishes,
                createWish,
                deleteWishById,
                editWishById,
                wishDetails,
                wishBlessings,
                fetchWishDetails,
                getWishBlessings,
                fetchPinnedWishes,
                pinUnpinWishById,
                pinnedWishes
            }}
        >
            {children}
        </WishContext.Provider>
    );
};

export const useWishes = () => useContext(WishContext);
