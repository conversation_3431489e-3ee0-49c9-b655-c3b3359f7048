"use client";

import { createContext, useContext, useState, useCallback } from "react";
import { getCompassionMapData } from "@/services/api";

const CompassionMapContext = createContext();

export const CompassionMapProvider = ({ children }) => {
    const [mapData, setMapData] = useState({
        wishData: [],
        blessData: [],
        gratitudeData: [],
        totalBlessDuration: 0,
        totalwish: 0,
        totalGratitude: 0,
        mostUsedLocation: null,
        circleData: null,
    });
    const [loadingMapData, setLoadingMapData] = useState(false);
    const [mapError, setMapError] = useState(null);
    const [selectedFilter, setSelectedFilter] = useState(2);

    const fetchMapData = useCallback(async (accessToken, type = 2) => {
        if (!accessToken) {
            console.warn("⚠️ Cannot fetch map data: no access token");
            return;
        }
        setLoadingMapData(true);
        setMapError(null);
        try {
            const data = await getCompassionMapData(accessToken, type);
            console.log("✅ Compassion Provider map data loaded:", data);
            setMapData(data);
            setSelectedFilter(type);
        } catch (err) {
            setMapError(err);
        } finally {
            setLoadingMapData(false);
        }
    }, []);

    return (
        <CompassionMapContext.Provider
            value={{
                mapData,
                loadingMapData,
                mapError,
                selectedFilter,
                fetchMapData,
            }}
        >
            {children}
        </CompassionMapContext.Provider>
    );
};

export const useCompassionMap = () => useContext(CompassionMapContext);
